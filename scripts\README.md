# 諮詢系統工具集

這個目錄包含了諮詢預約系統的各種管理和測試工具。

## 🛠️ 工具列表

### 1. 負載測試工具 (load_test.go)
用於測試系統在高負載下的效能表現。

**功能：**
- 模擬多並發使用者
- 測試各種 API 端點
- 生成詳細的效能報告
- 支援壓力測試模式

**使用方法：**
```bash
go run scripts/run_tool.go load-test
```

### 2. 資料遷移驗證工具 (migration_validator.go)
驗證資料遷移的完整性和正確性。

**功能：**
- 檢查資料表結構
- 驗證資料完整性
- 檢查外鍵關聯
- 生成驗證報告

**使用方法：**
```bash
go run scripts/run_tool.go migration-validator
```

### 3. 效能優化工具 (performance_optimization.go)
自動化的資料庫效能優化。

**功能：**
- 建立資料庫索引
- 分析表格統計
- 優化常用查詢
- 清理無用資料

**使用方法：**
```bash
go run scripts/run_tool.go performance-optimization
```

### 4. 回滾管理工具 (rollback_manager.go)
管理系統備份和回滾操作。

**功能：**
- 建立資料備份
- 列出可用備份
- 執行系統回滾
- 清理舊備份

**使用方法：**
```bash
go run scripts/run_tool.go rollback-manager
```

### 5. 系統監控工具 (system_monitor.go)
即時監控系統狀態和效能指標。

**功能：**
- 收集系統指標
- 提供 Web 監控介面
- 自動警告機制
- 效能分析報告

**使用方法：**
```bash
go run scripts/run_tool.go system-monitor
```

## 🚀 快速開始

### 執行單一工具
```bash
# 執行負載測試
go run scripts/run_tool.go load-test

# 執行系統監控
go run scripts/run_tool.go system-monitor
```

### 查看可用工具
```bash
go run scripts/run_tool.go
```

## 📋 測試腳本

### Windows 環境
```batch
scripts\run_tests.bat
```

### Linux/macOS 環境
```bash
scripts/run_tests.sh
```

## ⚠️ 注意事項

1. **資料庫連接**：確保工具能夠連接到正確的資料庫
2. **權限要求**：某些工具需要管理員權限
3. **備份建議**：在執行破壞性操作前建立備份
4. **環境隔離**：建議在測試環境中先行驗證

## 🔧 配置說明

### 資料庫連接
大部分工具使用以下預設連接字串：
```
root:forwork0926@tcp(localhost:3306)/grace?parseTime=True&loc=Local&charset=utf8mb4&collation=utf8mb4_unicode_ci
```

如需修改，請編輯對應工具檔案中的 DSN 設定。

### 監控服務
系統監控工具預設在 8080 埠提供 Web 介面：
- 監控面板：http://localhost:8080
- 指標 API：http://localhost:8080/metrics
- 健康檢查：http://localhost:8080/health

## 📊 工具輸出

### 負載測試報告
- 總請求數和成功率
- 平均/最小/最大回應時間
- 每秒請求數 (RPS)
- 錯誤率和錯誤詳情
- 效能評估建議

### 遷移驗證報告
- 表格完整性檢查
- 資料一致性驗證
- 外鍵關聯檢查
- 問題摘要和修復建議

### 效能優化報告
- 索引建立狀況
- 查詢效能分析
- 資料清理結果
- 優化建議

## 🐛 故障排除

### 常見問題

1. **連接資料庫失敗**
   - 檢查資料庫服務是否運行
   - 確認連接字串正確
   - 驗證使用者權限

2. **工具執行失敗**
   - 確保 Go 環境正確安裝
   - 檢查相依套件是否完整
   - 查看錯誤訊息詳情

3. **權限不足**
   - 以管理員身份執行
   - 檢查檔案系統權限
   - 確認資料庫操作權限

### 日誌位置
工具執行日誌通常輸出到標準輸出，部分工具會生成報告檔案：
- 效能報告：`performance_report_*.md`
- 驗證報告：`migration_validation_report.json`
- 備份檔案：`./backups/backup_*.sql`

## 📞 技術支援

如遇到問題，請檢查：
1. 工具執行日誌
2. 系統錯誤訊息
3. 資料庫連接狀態
4. 相關配置檔案

建議在生產環境使用前，先在測試環境充分驗證所有工具的功能。
