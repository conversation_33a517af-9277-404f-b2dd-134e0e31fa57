package controllers

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"

	. "cx/app/models"
	"cx/app/services"
)

// AdminConsultationV2PageController 後台諮詢系統頁面控制器
type AdminConsultationV2PageController struct {
	db      *gorm.DB
	service *services.ConsultationV2Service
}

// NewAdminConsultationV2PageController 建立後台諮詢系統頁面控制器
func NewAdminConsultationV2PageController(db *gorm.DB) *AdminConsultationV2PageController {
	return &AdminConsultationV2PageController{
		db:      db,
		service: services.NewConsultationV2Service(db),
	}
}

// ShowDashboard 顯示總覽頁面
func (c *AdminConsultationV2PageController) ShowDashboard(ctx *gin.Context) {
	ctx.HTML(http.StatusOK, "admin/consultation_v2/dashboard", gin.H{
		"Title": "諮詢系統總覽",
	})
}

// ShowModules 顯示模組管理頁面
func (c *AdminConsultationV2PageController) ShowModules(ctx *gin.Context) {
	ctx.HTML(http.StatusOK, "admin/consultation_v2/modules", gin.H{
		"Title": "模組管理",
	})
}

// ShowAppointments 顯示預約管理頁面
func (c *AdminConsultationV2PageController) ShowAppointments(ctx *gin.Context) {
	ctx.HTML(http.StatusOK, "admin/consultation_v2/appointments", gin.H{
		"Title": "預約管理",
	})
}

// ShowTimeSlots 顯示時段管理頁面
func (c *AdminConsultationV2PageController) ShowTimeSlots(ctx *gin.Context) {
	ctx.HTML(http.StatusOK, "admin/consultation_v2/time_slots", gin.H{
		"Title": "時段管理",
	})
}

// ShowModuleAssignments 顯示模組作業管理頁面
func (c *AdminConsultationV2PageController) ShowModuleAssignments(ctx *gin.Context) {
	moduleIDStr := ctx.Param("id")
	moduleID, err := strconv.ParseUint(moduleIDStr, 10, 32)
	if err != nil {
		ctx.HTML(http.StatusBadRequest, "error", gin.H{
			"Title": "錯誤",
			"Error": "無效的模組ID",
		})
		return
	}

	// 取得模組資訊
	var module ConsultationModule
	if err := c.db.First(&module, moduleID).Error; err != nil {
		ctx.HTML(http.StatusNotFound, "error", gin.H{
			"Title": "錯誤",
			"Error": "模組不存在",
		})
		return
	}

	ctx.HTML(http.StatusOK, "admin/consultation_v2/module_assignments", gin.H{
		"Title":  "作業管理",
		"Module": module,
	})
}

// ShowModuleRules 顯示模組資格規則管理頁面
func (c *AdminConsultationV2PageController) ShowModuleRules(ctx *gin.Context) {
	moduleIDStr := ctx.Param("id")
	moduleID, err := strconv.ParseUint(moduleIDStr, 10, 32)
	if err != nil {
		ctx.HTML(http.StatusBadRequest, "error", gin.H{
			"Title": "錯誤",
			"Error": "無效的模組ID",
		})
		return
	}

	// 取得模組資訊
	var module ConsultationModule
	if err := c.db.First(&module, moduleID).Error; err != nil {
		ctx.HTML(http.StatusNotFound, "error", gin.H{
			"Title": "錯誤",
			"Error": "模組不存在",
		})
		return
	}

	ctx.HTML(http.StatusOK, "admin/consultation_v2/module_rules", gin.H{
		"Title":  "資格規則管理",
		"Module": module,
	})
}

// ShowAssignmentSubmissions 顯示作業提交管理頁面
func (c *AdminConsultationV2PageController) ShowAssignmentSubmissions(ctx *gin.Context) {
	ctx.HTML(http.StatusOK, "admin/consultation_v2/assignment_submissions", gin.H{
		"Title": "作業審核",
	})
}

// ShowStatistics 顯示統計報表頁面
func (c *AdminConsultationV2PageController) ShowStatistics(ctx *gin.Context) {
	ctx.HTML(http.StatusOK, "admin/consultation_v2/statistics", gin.H{
		"Title": "統計報表",
	})
}

// ShowSettings 顯示系統設定頁面
func (c *AdminConsultationV2PageController) ShowSettings(ctx *gin.Context) {
	ctx.HTML(http.StatusOK, "admin/consultation_v2/settings", gin.H{
		"Title": "系統設定",
	})
}

// ShowAppointmentDetail 顯示預約詳情頁面
func (c *AdminConsultationV2PageController) ShowAppointmentDetail(ctx *gin.Context) {
	appointmentIDStr := ctx.Param("id")
	appointmentID, err := strconv.ParseUint(appointmentIDStr, 10, 32)
	if err != nil {
		ctx.HTML(http.StatusBadRequest, "error", gin.H{
			"Title": "錯誤",
			"Error": "無效的預約ID",
		})
		return
	}

	// 取得預約資訊
	var appointment ConsultationAppointment
	if err := c.db.Preload("Module").Preload("Member").
		First(&appointment, appointmentID).Error; err != nil {
		ctx.HTML(http.StatusNotFound, "error", gin.H{
			"Title": "錯誤",
			"Error": "預約不存在",
		})
		return
	}

	ctx.HTML(http.StatusOK, "admin/consultation_v2/appointment_detail", gin.H{
		"Title":       "預約詳情",
		"Appointment": appointment,
	})
}

// ShowMemberDetail 顯示會員詳情頁面
func (c *AdminConsultationV2PageController) ShowMemberDetail(ctx *gin.Context) {
	memberIDStr := ctx.Param("id")
	memberID, err := strconv.ParseUint(memberIDStr, 10, 32)
	if err != nil {
		ctx.HTML(http.StatusBadRequest, "error", gin.H{
			"Title": "錯誤",
			"Error": "無效的會員ID",
		})
		return
	}

	// 取得會員資訊
	var member Member
	if err := c.db.First(&member, memberID).Error; err != nil {
		ctx.HTML(http.StatusNotFound, "error", gin.H{
			"Title": "錯誤",
			"Error": "會員不存在",
		})
		return
	}

	// 取得會員的預約記錄
	var appointments []ConsultationAppointment
	c.db.Preload("Module").Where("member_id = ?", memberID).
		Order("appointment_datetime DESC").Find(&appointments)

	// 取得會員的作業提交記錄
	var submissions []MemberAssignmentSubmission
	c.db.Preload("Assignment").Preload("Assignment.Module").
		Where("member_id = ?", memberID).
		Order("submitted_at DESC").Find(&submissions)

	ctx.HTML(http.StatusOK, "admin/consultation_v2/member_detail", gin.H{
		"Title":       "會員詳情",
		"Member":      member,
		"Appointments": appointments,
		"Submissions": submissions,
	})
}

// ShowReports 顯示報表頁面
func (c *AdminConsultationV2PageController) ShowReports(ctx *gin.Context) {
	reportType := ctx.Query("type")
	
	ctx.HTML(http.StatusOK, "admin/consultation_v2/reports", gin.H{
		"Title":      "報表中心",
		"ReportType": reportType,
	})
}

// ShowExportData 顯示資料匯出頁面
func (c *AdminConsultationV2PageController) ShowExportData(ctx *gin.Context) {
	ctx.HTML(http.StatusOK, "admin/consultation_v2/export", gin.H{
		"Title": "資料匯出",
	})
}

// ShowSystemLogs 顯示系統日誌頁面
func (c *AdminConsultationV2PageController) ShowSystemLogs(ctx *gin.Context) {
	ctx.HTML(http.StatusOK, "admin/consultation_v2/logs", gin.H{
		"Title": "系統日誌",
	})
}

// ShowBackup 顯示備份管理頁面
func (c *AdminConsultationV2PageController) ShowBackup(ctx *gin.Context) {
	ctx.HTML(http.StatusOK, "admin/consultation_v2/backup", gin.H{
		"Title": "備份管理",
	})
}

// ShowMigration 顯示資料遷移頁面
func (c *AdminConsultationV2PageController) ShowMigration(ctx *gin.Context) {
	ctx.HTML(http.StatusOK, "admin/consultation_v2/migration", gin.H{
		"Title": "資料遷移",
	})
}

// ShowNotifications 顯示通知管理頁面
func (c *AdminConsultationV2PageController) ShowNotifications(ctx *gin.Context) {
	ctx.HTML(http.StatusOK, "admin/consultation_v2/notifications", gin.H{
		"Title": "通知管理",
	})
}

// ShowEmailTemplates 顯示郵件模板管理頁面
func (c *AdminConsultationV2PageController) ShowEmailTemplates(ctx *gin.Context) {
	ctx.HTML(http.StatusOK, "admin/consultation_v2/email_templates", gin.H{
		"Title": "郵件模板",
	})
}

// ShowIntegrations 顯示外部整合管理頁面
func (c *AdminConsultationV2PageController) ShowIntegrations(ctx *gin.Context) {
	ctx.HTML(http.StatusOK, "admin/consultation_v2/integrations", gin.H{
		"Title": "外部整合",
	})
}

// ShowPermissions 顯示權限管理頁面
func (c *AdminConsultationV2PageController) ShowPermissions(ctx *gin.Context) {
	ctx.HTML(http.StatusOK, "admin/consultation_v2/permissions", gin.H{
		"Title": "權限管理",
	})
}

// ShowAuditLogs 顯示操作日誌頁面
func (c *AdminConsultationV2PageController) ShowAuditLogs(ctx *gin.Context) {
	ctx.HTML(http.StatusOK, "admin/consultation_v2/audit_logs", gin.H{
		"Title": "操作日誌",
	})
}
