package models

import (
	"cx/app/utils"
	"mime/multipart"
	"strings"
	"time"

	"gopkg.in/guregu/null.v4"
	"gorm.io/gorm"
)

const (
	WorkPath       = "/uploads/works"
	MemberWorkPath = "uploads/member_works"
)

var WorkOrderImportMap = map[string]string{
	"作業名稱": "work_title",
	"會員帳號": "uid",
	"上傳時間": "upload_at",
	"檔名":   "upload_file",
}

type Work struct {
	ID            uint                  `gorm:"primary_key" form:"id" json:"id"`
	ProID         uint                  `form:"pro_id" json:"pro_id"`
	WorkTitle     string                `form:"work_title" json:"work_title"`
	WorkType      string                `form:"work_type" json:"work_type"` // P:課前作業，N:課後作業，C:輔導單
	WorkDesc      string                `form:"work_desc" json:"work_desc"`
	WorkFile      string                `form:"work_file" json:"work_file"`
	DelFile       bool                  `gorm:"-" form:"del_file" json:"del_file"`
	File          *multipart.FileHeader `gorm:"-" form:"file" json:"file"`
	DeadlineLimit string                `form:"deadline_limit" json:"deadline_limit"` // N:依課程截止日，F:天數，D:日期
	DeadlineDays  int                   `form:"deadline_days" json:"deadline_days"`
	DeadlineDate  null.Time             `form:"deadline_date" json:"deadline_date"`
	Status        string                `form:"status" json:"status"`
	CreatedByID   uint                  `gorm:"<-:create" form:"created_by_id" json:"created_by_id"`
	CreatedBy     string                `gorm:"-" json:"created_by"`
	UpdatedByID   uint                  `form:"updated_by_id" json:"updated_by_id"`
	UpdatedBy     string                `gorm:"-" json:"updated_by"`
	CreatedAt     time.Time             `gorm:"<-:create" form:"created_at" json:"created_at"`
	UpdatedAt     time.Time             `form:"updated_at" json:"updated_at"`
	DeletedAt     gorm.DeletedAt        `gorm:"<-:false" form:"deleted_at" json:"deleted_at"`
}

type WorkList struct {
	ID        uint      `gorm:"primary_key" form:"id" json:"id"`
	WorkTitle string    `form:"work_title" json:"work_title"`
	WorkType  string    `form:"work_type" json:"work_type"`
	ProID     uint      `form:"pro_id" json:"pro_id"`
	ProName   string    `form:"pro_name" json:"pro_name"`
	CreatedAt time.Time `form:"created_at" json:"created_at"`
}

func (WorkList) TableName() string {
	return "works"
}

type WorkExport struct {
	WorkTitle string `json:"作業名稱"`
	ProName   string `json:"課程名稱"`
	WorkDesc  string `json:"作業說明"`
	CreatedAt string `json:"建立時間"`
	Status    string `json:"狀態"`
}

func (WorkExport) TableName() string {
	return "works"
}

func (work *WorkExport) HandleData() {
	work.CreatedAt = utils.ParseStrDate(work.CreatedAt, "2006/01/02 15:04:05")

	if work.Status == "Y" {
		work.Status = "啟用"
	} else {
		work.Status = "停用"
	}
}

type MemberWork struct {
	ID         uint           `gorm:"primary_key" form:"id" json:"id"`
	MemberID   uint           `gorm:"<-:create" form:"member_id" json:"member_id"`
	MemProID   uint           `gorm:"<-:create" form:"mem_pro_id" json:"mem_pro_id"`
	WorkID     uint           `gorm:"<-:create" form:"work_id" json:"work_id"`
	UploadFile string         `form:"upload_file" json:"upload_file"`
	UploadAt   null.Time      `form:"upload_at" json:"upload_at"`
	DeadlineAt null.Time      `form:"deadline_at" json:"deadline_at"`
	Status     string         `gorm:"<-:update" form:"status" json:"status"` // W:待審核，Y:已通過，N:未批准，C:取消資格
	CreatedAt  time.Time      `gorm:"<-:create" form:"created_at" json:"created_at"`
	UpdatedAt  time.Time      `form:"updated_at" json:"updated_at"`
	DeletedAt  gorm.DeletedAt `gorm:"<-:false" form:"deleted_at" json:"deleted_at"`
}

func (memWork *MemberWork) IsOverdue() bool {
	if memWork.DeadlineAt.IsZero() {
		return false
	}

	return time.Now().After(memWork.DeadlineAt.Time) || time.Now().Equal(memWork.DeadlineAt.Time)
}

func InitMemWorks(conn *gorm.DB, memPro *MemberProduct) error {
	works := []Work{}
	memWorks := []MemberWork{}

	if err := conn.Select("id", "deadline_limit", "deadline_days", "deadline_date").
		Where("pro_id = ?", memPro.ProductID).
		Where("status = 'Y'").
		Find(&works).Error; err != nil {
		return err
	}

	if len(works) == 0 {
		return nil
	}

	for _, work := range works {
		memWork := MemberWork{
			MemberID: memPro.MemberID,
			MemProID: memPro.ID,
			WorkID:   work.ID,
		}

		if work.DeadlineLimit == "N" {
			t := memPro.ExpiredAt
			memWork.DeadlineAt = null.TimeFrom(time.Date(t.Year(), t.Month(), t.Day(), 23, 59, 59, 0, t.Location()))
		} else if work.DeadlineLimit == "F" {
			t := time.Now().AddDate(0, 0, work.DeadlineDays)
			memWork.DeadlineAt = null.TimeFrom(time.Date(t.Year(), t.Month(), t.Day(), 23, 59, 59, 0, t.Location()))
		} else if work.DeadlineLimit == "D" {
			memWork.DeadlineAt = work.DeadlineDate
		}

		memWorks = append(memWorks, memWork)
	}

	if err := conn.Create(&memWorks).Error; err != nil {
		return err
	}

	return nil
}

type MemberWorkList struct {
	WorkID        uint      `form:"work_id" json:"work_id"`
	WorkTitle     string    `form:"work_title" json:"work_title"`
	WorkType      string    `form:"work_type" json:"work_type"`
	WorkDesc      string    `form:"work_desc" json:"work_desc"`
	WorkFile      string    `form:"work_file" json:"work_file"`
	MemWorkID     uint      `form:"mem_work_id" json:"mem_work_id"`
	MemProID      uint      `form:"mem_pro_id" json:"mem_pro_id"`
	MemName       string    `form:"mem_name" json:"mem_name"`
	ProID         uint      `form:"pro_id" json:"pro_id"`
	ProName       string    `form:"pro_name" json:"pro_name"`
	ProStatus     string    `form:"pro_status" json:"pro_status"`
	MemProStatus  string    `form:"mem_pro_status" json:"mem_pro_status"`
	MemWorkStatus string    `form:"mem_work_status" json:"mem_work_status"`
	ProExpiredAt  string    `form:"pro_expired_at" json:"pro_expired_at"`
	DeadlineAt    string    `form:"deadline_at" json:"deadline_at"`
	UploadFile    string    `form:"upload_file" json:"upload_file"`
	UploadAt      null.Time `form:"upload_at" json:"upload_at"`
}

func (MemberWorkList) TableName() string {
	return "works"
}

type WorkOrderList struct {
	MemberWork
	MemberID   uint   `form:"member_id" json:"member_id"`
	MemberName string `form:"member_name" json:"member_name"`
	Uid        string `form:"uid" json:"uid"`
}

func (WorkOrderList) TableName() string {
	return "member_works"
}

type WorkOrderExport struct {
	ProName    string `json:"課程名稱" col:"20"`
	WorkTitle  string `json:"作業名稱" col:"20"`
	Uid        string `json:"會員帳號" col:"20"`
	MemName    string `json:"姓名" col:"10"`
	IgID       string `json:"Instagram" col:"20"`
	UploadAt   string `json:"上傳時間" col:"20"`
	Status     string `json:"狀態" col:"10"`
	UploadFile string `json:"檔名" col:"20"`
}

func (WorkOrderExport) TableName() string {
	return "member_products"
}

func (work *WorkOrderExport) HandleData() {
	work.UploadAt = utils.ParseStrDate(work.UploadAt, "2006/01/02 15:04:05")

	switch work.Status {
	case "W":
		work.Status = "待審核"
	case "Y":
		work.Status = "已通過"
	case "N":
		work.Status = "未批准"
	case "C":
		work.Status = "取消資格"
	}

	if strings.Index(work.UploadFile, "http") < 0 {
		work.UploadFile = utils.GetDomain() + work.UploadFile
	}
}
