//go:build ignore
// +build ignore

package main

import (
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"runtime"
	"time"

	"gorm.io/driver/mysql"
	"gorm.io/gorm"

	. "cx/app/models"
)

// SystemMonitor 系統監控器
type SystemMonitor struct {
	db        *gorm.DB
	startTime time.Time
}

// SystemMetrics 系統指標
type SystemMetrics struct {
	Timestamp time.Time `json:"timestamp"`

	// 系統資源
	MemoryUsage     MemoryMetrics   `json:"memory_usage"`
	DatabaseMetrics DatabaseMetrics `json:"database_metrics"`

	// 業務指標
	BusinessMetrics BusinessMetrics `json:"business_metrics"`

	// 效能指標
	PerformanceMetrics PerformanceMetrics `json:"performance_metrics"`
}

// MemoryMetrics 記憶體指標
type MemoryMetrics struct {
	AllocMB      float64 `json:"alloc_mb"`
	TotalAllocMB float64 `json:"total_alloc_mb"`
	SysMB        float64 `json:"sys_mb"`
	NumGC        uint32  `json:"num_gc"`
	Goroutines   int     `json:"goroutines"`
}

// DatabaseMetrics 資料庫指標
type DatabaseMetrics struct {
	ConnectionsActive int         `json:"connections_active"`
	ConnectionsIdle   int         `json:"connections_idle"`
	QueriesPerSecond  float64     `json:"queries_per_second"`
	SlowQueries       int         `json:"slow_queries"`
	TableSizes        []TableSize `json:"table_sizes"`
	QueryStats        []QueryStat `json:"query_stats"`
}

// BusinessMetrics 業務指標
type BusinessMetrics struct {
	TotalModules        int64 `json:"total_modules"`
	ActiveModules       int64 `json:"active_modules"`
	TotalAppointments   int64 `json:"total_appointments"`
	PendingAppointments int64 `json:"pending_appointments"`
	TodayAppointments   int64 `json:"today_appointments"`
	TotalSubmissions    int64 `json:"total_submissions"`
	PendingSubmissions  int64 `json:"pending_submissions"`
	TotalMembers        int64 `json:"total_members"`
	ActiveMembers       int64 `json:"active_members"`
	TotalFiles          int64 `json:"total_files"`
	TotalFileSize       int64 `json:"total_file_size"`
}

// PerformanceMetrics 效能指標
type PerformanceMetrics struct {
	AverageResponseTime float64 `json:"average_response_time_ms"`
	RequestsPerSecond   float64 `json:"requests_per_second"`
	ErrorRate           float64 `json:"error_rate"`
	UptimeSeconds       float64 `json:"uptime_seconds"`
}

// TableSize 表格大小
type TableSize struct {
	TableName string `json:"table_name"`
	Rows      int64  `json:"rows"`
	DataSize  int64  `json:"data_size"`
	IndexSize int64  `json:"index_size"`
}

// QueryStat 查詢統計
type QueryStat struct {
	QueryType string  `json:"query_type"`
	Count     int64   `json:"count"`
	AvgTime   float64 `json:"avg_time_ms"`
	TotalTime float64 `json:"total_time_ms"`
}

// NewSystemMonitor 建立系統監控器
func NewSystemMonitor() *SystemMonitor {
	// 連接資料庫
	dsn := "root:forwork0926@tcp(localhost:3306)/grace?parseTime=True&loc=Local&charset=utf8mb4&collation=utf8mb4_unicode_ci"
	db, err := gorm.Open(mysql.Open(dsn), &gorm.Config{})
	if err != nil {
		log.Fatal("連接資料庫失敗:", err)
	}

	return &SystemMonitor{
		db:        db,
		startTime: time.Now(),
	}
}

// CollectMetrics 收集系統指標
func (m *SystemMonitor) CollectMetrics() (*SystemMetrics, error) {
	metrics := &SystemMetrics{
		Timestamp: time.Now(),
	}

	// 收集記憶體指標
	metrics.MemoryUsage = m.collectMemoryMetrics()

	// 收集資料庫指標
	var err error
	metrics.DatabaseMetrics, err = m.collectDatabaseMetrics()
	if err != nil {
		return nil, fmt.Errorf("收集資料庫指標失敗: %w", err)
	}

	// 收集業務指標
	metrics.BusinessMetrics, err = m.collectBusinessMetrics()
	if err != nil {
		return nil, fmt.Errorf("收集業務指標失敗: %w", err)
	}

	// 收集效能指標
	metrics.PerformanceMetrics = m.collectPerformanceMetrics()

	return metrics, nil
}

// collectMemoryMetrics 收集記憶體指標
func (m *SystemMonitor) collectMemoryMetrics() MemoryMetrics {
	var memStats runtime.MemStats
	runtime.ReadMemStats(&memStats)

	return MemoryMetrics{
		AllocMB:      float64(memStats.Alloc) / 1024 / 1024,
		TotalAllocMB: float64(memStats.TotalAlloc) / 1024 / 1024,
		SysMB:        float64(memStats.Sys) / 1024 / 1024,
		NumGC:        memStats.NumGC,
		Goroutines:   runtime.NumGoroutine(),
	}
}

// collectDatabaseMetrics 收集資料庫指標
func (m *SystemMonitor) collectDatabaseMetrics() (DatabaseMetrics, error) {
	metrics := DatabaseMetrics{}

	// 取得連接池狀態
	sqlDB, err := m.db.DB()
	if err != nil {
		return metrics, err
	}

	stats := sqlDB.Stats()
	metrics.ConnectionsActive = stats.InUse
	metrics.ConnectionsIdle = stats.Idle

	// 取得表格大小
	tableSizes, err := m.getTableSizes()
	if err != nil {
		return metrics, err
	}
	metrics.TableSizes = tableSizes

	// 取得查詢統計（簡化版本）
	queryStats, err := m.getQueryStats()
	if err != nil {
		return metrics, err
	}
	metrics.QueryStats = queryStats

	return metrics, nil
}

// collectBusinessMetrics 收集業務指標
func (m *SystemMonitor) collectBusinessMetrics() (BusinessMetrics, error) {
	metrics := BusinessMetrics{}

	// 模組統計
	m.db.Model(&ConsultationModule{}).Count(&metrics.TotalModules)
	m.db.Model(&ConsultationModule{}).Where("is_active = ?", true).Count(&metrics.ActiveModules)

	// 預約統計
	m.db.Model(&ConsultationAppointment{}).Count(&metrics.TotalAppointments)
	m.db.Model(&ConsultationAppointment{}).Where("status = ?", "pending").Count(&metrics.PendingAppointments)

	today := time.Now().Format("2006-01-02")
	m.db.Model(&ConsultationAppointment{}).Where("DATE(appointment_datetime) = ?", today).Count(&metrics.TodayAppointments)

	// 作業提交統計
	m.db.Model(&MemberAssignmentSubmission{}).Count(&metrics.TotalSubmissions)
	m.db.Model(&MemberAssignmentSubmission{}).Where("status = ?", "pending").Count(&metrics.PendingSubmissions)

	// 會員統計
	m.db.Model(&Member{}).Count(&metrics.TotalMembers)
	m.db.Model(&Member{}).Where("status = ?", "Y").Count(&metrics.ActiveMembers)

	// 檔案統計
	m.db.Model(&FileUpload{}).Count(&metrics.TotalFiles)

	var totalSize struct {
		Total int64
	}
	m.db.Model(&FileUpload{}).Select("SUM(file_size) as total").Scan(&totalSize)
	metrics.TotalFileSize = totalSize.Total

	return metrics, nil
}

// collectPerformanceMetrics 收集效能指標
func (m *SystemMonitor) collectPerformanceMetrics() PerformanceMetrics {
	return PerformanceMetrics{
		UptimeSeconds: time.Since(m.startTime).Seconds(),
		// 其他指標需要在實際應用中實現
		AverageResponseTime: 0, // 需要中間件收集
		RequestsPerSecond:   0, // 需要中間件收集
		ErrorRate:           0, // 需要中間件收集
	}
}

// getTableSizes 取得表格大小
func (m *SystemMonitor) getTableSizes() ([]TableSize, error) {
	var sizes []TableSize

	rows, err := m.db.Raw(`
		SELECT 
			TABLE_NAME as table_name,
			TABLE_ROWS as rows,
			DATA_LENGTH as data_size,
			INDEX_LENGTH as index_size
		FROM information_schema.TABLES 
		WHERE TABLE_SCHEMA = DATABASE() 
		AND (TABLE_NAME LIKE 'consultation_%' OR TABLE_NAME = 'file_uploads' OR TABLE_NAME = 'members')
		ORDER BY DATA_LENGTH DESC
	`).Rows()
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	for rows.Next() {
		var size TableSize
		if err := rows.Scan(&size.TableName, &size.Rows, &size.DataSize, &size.IndexSize); err != nil {
			continue
		}
		sizes = append(sizes, size)
	}

	return sizes, nil
}

// getQueryStats 取得查詢統計
func (m *SystemMonitor) getQueryStats() ([]QueryStat, error) {
	// 這是一個簡化的實現，實際上需要啟用 MySQL 的 performance_schema
	stats := []QueryStat{
		{QueryType: "SELECT", Count: 0, AvgTime: 0, TotalTime: 0},
		{QueryType: "INSERT", Count: 0, AvgTime: 0, TotalTime: 0},
		{QueryType: "UPDATE", Count: 0, AvgTime: 0, TotalTime: 0},
		{QueryType: "DELETE", Count: 0, AvgTime: 0, TotalTime: 0},
	}

	return stats, nil
}

// StartHTTPServer 啟動 HTTP 監控服務
func (m *SystemMonitor) StartHTTPServer(port string) {
	http.HandleFunc("/metrics", func(w http.ResponseWriter, r *http.Request) {
		metrics, err := m.CollectMetrics()
		if err != nil {
			http.Error(w, err.Error(), http.StatusInternalServerError)
			return
		}

		w.Header().Set("Content-Type", "application/json")
		json.NewEncoder(w).Encode(metrics)
	})

	http.HandleFunc("/health", func(w http.ResponseWriter, r *http.Request) {
		// 簡單的健康檢查
		if err := m.db.Exec("SELECT 1").Error; err != nil {
			http.Error(w, "Database connection failed", http.StatusServiceUnavailable)
			return
		}

		w.Header().Set("Content-Type", "application/json")
		json.NewEncoder(w).Encode(map[string]interface{}{
			"status":    "healthy",
			"timestamp": time.Now(),
			"uptime":    time.Since(m.startTime).String(),
		})
	})

	http.HandleFunc("/", func(w http.ResponseWriter, r *http.Request) {
		html := `
<!DOCTYPE html>
<html>
<head>
    <title>諮詢系統監控</title>
    <meta charset="UTF-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .metric-card { border: 1px solid #ddd; padding: 20px; margin: 10px 0; border-radius: 5px; }
        .metric-title { font-weight: bold; color: #333; }
        .metric-value { font-size: 24px; color: #007bff; }
        .status-healthy { color: #28a745; }
        .status-warning { color: #ffc107; }
        .status-error { color: #dc3545; }
    </style>
    <script>
        function refreshMetrics() {
            fetch('/metrics')
                .then(response => response.json())
                .then(data => {
                    document.getElementById('timestamp').textContent = new Date(data.timestamp).toLocaleString();
                    document.getElementById('memory-alloc').textContent = data.memory_usage.alloc_mb.toFixed(2) + ' MB';
                    document.getElementById('goroutines').textContent = data.memory_usage.goroutines;
                    document.getElementById('total-modules').textContent = data.business_metrics.total_modules;
                    document.getElementById('pending-appointments').textContent = data.business_metrics.pending_appointments;
                    document.getElementById('pending-submissions').textContent = data.business_metrics.pending_submissions;
                    document.getElementById('uptime').textContent = (data.performance_metrics.uptime_seconds / 3600).toFixed(2) + ' 小時';
                });
        }
        
        setInterval(refreshMetrics, 5000); // 每5秒更新一次
        window.onload = refreshMetrics;
    </script>
</head>
<body>
    <h1>🚀 諮詢系統監控面板</h1>
    <p>最後更新: <span id="timestamp">-</span></p>
    
    <div class="metric-card">
        <div class="metric-title">系統資源</div>
        <p>記憶體使用: <span class="metric-value" id="memory-alloc">-</span></p>
        <p>Goroutines: <span class="metric-value" id="goroutines">-</span></p>
        <p>運行時間: <span class="metric-value" id="uptime">-</span></p>
    </div>
    
    <div class="metric-card">
        <div class="metric-title">業務指標</div>
        <p>總模組數: <span class="metric-value" id="total-modules">-</span></p>
        <p>待確認預約: <span class="metric-value" id="pending-appointments">-</span></p>
        <p>待審核作業: <span class="metric-value" id="pending-submissions">-</span></p>
    </div>
    
    <div class="metric-card">
        <div class="metric-title">API 端點</div>
        <p><a href="/metrics">/metrics</a> - JSON 格式的完整指標</p>
        <p><a href="/health">/health</a> - 健康檢查</p>
    </div>
</body>
</html>`
		w.Header().Set("Content-Type", "text/html")
		w.Write([]byte(html))
	})

	fmt.Printf("🌐 監控服務已啟動: http://localhost:%s\n", port)
	fmt.Printf("📊 指標端點: http://localhost:%s/metrics\n", port)
	fmt.Printf("💚 健康檢查: http://localhost:%s/health\n", port)

	log.Fatal(http.ListenAndServe(":"+port, nil))
}

// RunPeriodicCollection 定期收集指標
func (m *SystemMonitor) RunPeriodicCollection(interval time.Duration) {
	ticker := time.NewTicker(interval)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			metrics, err := m.CollectMetrics()
			if err != nil {
				log.Printf("收集指標失敗: %v", err)
				continue
			}

			// 檢查警告條件
			m.checkAlerts(metrics)

			// 可以在這裡將指標寫入日誌或發送到監控系統
			log.Printf("指標收集完成 - 記憶體: %.2f MB, 待審核: %d, 待確認: %d",
				metrics.MemoryUsage.AllocMB,
				metrics.BusinessMetrics.PendingSubmissions,
				metrics.BusinessMetrics.PendingAppointments)
		}
	}
}

// checkAlerts 檢查警告條件
func (m *SystemMonitor) checkAlerts(metrics *SystemMetrics) {
	// 記憶體使用警告
	if metrics.MemoryUsage.AllocMB > 500 {
		log.Printf("⚠️  記憶體使用過高: %.2f MB", metrics.MemoryUsage.AllocMB)
	}

	// 待處理項目警告
	if metrics.BusinessMetrics.PendingAppointments > 10 {
		log.Printf("⚠️  待確認預約過多: %d 個", metrics.BusinessMetrics.PendingAppointments)
	}

	if metrics.BusinessMetrics.PendingSubmissions > 20 {
		log.Printf("⚠️  待審核作業過多: %d 個", metrics.BusinessMetrics.PendingSubmissions)
	}

	// Goroutine 洩漏警告
	if metrics.MemoryUsage.Goroutines > 1000 {
		log.Printf("⚠️  Goroutine 數量過多: %d 個", metrics.MemoryUsage.Goroutines)
	}
}

func main() {
	fmt.Println("🚀 諮詢系統監控器")
	fmt.Println("==================")

	monitor := NewSystemMonitor()

	// 啟動定期收集（在背景執行）
	go monitor.RunPeriodicCollection(30 * time.Second)

	// 啟動 HTTP 服務
	monitor.StartHTTPServer("8080")
}
