-- 諮詢模組表
CREATE TABLE IF NOT EXISTS `consultation_modules` (
    `id` BIGINT NOT NULL PRIMARY KEY AUTO_INCREMENT,
    `name` VARCHAR(255) NOT NULL COMMENT '諮詢模組名稱',
    `description` TEXT COMMENT '模組描述',
    `category` ENUM('basic_guidance', 'advanced_guidance', 'specialized_consultation', 'group_session', 'activity_gift') NOT NULL COMMENT '模組分類',
    `is_active` BOOLEAN DEFAULT TRUE COMMENT '是否啟用',
    `sort_order` INT DEFAULT 0 COMMENT '排序',
    `created_by_id` INT(10) UNSIGNED NOT NULL,
    `updated_by_id` INT(10) UNSIGNED NULL DEFAULT NULL,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX `idx_category` (`category`),
    INDEX `idx_is_active` (`is_active`),
    INDEX `idx_sort_order` (`sort_order`),
    CONSTRAINT `consultation_modules_created_by_id_fk` FOREIGN KEY (`created_by_id`) REFERENCES `admins` (`id`) ON DELETE CASCADE,
    CONSTRAINT `consultation_modules_updated_by_id_fk` FOREIGN KEY (`updated_by_id`) REFERENCES `admins` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='諮詢模組表';
