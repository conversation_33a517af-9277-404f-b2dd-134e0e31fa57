package models

import (
	"time"

	"gopkg.in/guregu/null.v4"
)

// ConsultationModuleCategory 諮詢模組分類
type ConsultationModuleCategory string

const (
	CategoryBasicGuidance           ConsultationModuleCategory = "basic_guidance"
	CategoryAdvancedGuidance        ConsultationModuleCategory = "advanced_guidance"
	CategorySpecializedConsultation ConsultationModuleCategory = "specialized_consultation"
	CategoryGroupSession            ConsultationModuleCategory = "group_session"
	CategoryActivityGift            ConsultationModuleCategory = "activity_gift"
)

// AssignmentSubmissionStatus 作業提交狀態
type AssignmentSubmissionStatus string

const (
	SubmissionStatusDraft     AssignmentSubmissionStatus = "draft"
	SubmissionStatusSubmitted AssignmentSubmissionStatus = "submitted"
	SubmissionStatusApproved  AssignmentSubmissionStatus = "approved"
	SubmissionStatusRejected  AssignmentSubmissionStatus = "rejected"
)

// ConsultationAppointmentStatus 預約狀態
type ConsultationAppointmentStatus string

const (
	AppointmentStatusPending   ConsultationAppointmentStatus = "pending"
	AppointmentStatusConfirmed ConsultationAppointmentStatus = "confirmed"
	AppointmentStatusCompleted ConsultationAppointmentStatus = "completed"
	AppointmentStatusCancelled ConsultationAppointmentStatus = "cancelled"
)

// ConsultationModule 諮詢模組實體
type ConsultationModule struct {
	ID          uint                       `gorm:"primaryKey" json:"id"`
	Name        string                     `gorm:"not null" json:"name"`
	Description string                     `json:"description"`
	Category    ConsultationModuleCategory `gorm:"type:enum('basic_guidance','advanced_guidance','specialized_consultation','group_session','activity_gift');not null" json:"category"`
	IsActive    bool                       `gorm:"default:true" json:"is_active"`
	SortOrder   int                        `gorm:"default:0" json:"sort_order"`
	CreatedByID uint                       `gorm:"not null" json:"created_by_id"`
	UpdatedByID null.Int                   `json:"updated_by_id"`
	CreatedAt   time.Time                  `json:"created_at"`
	UpdatedAt   time.Time                  `json:"updated_at"`

	// 關聯
	Assignments      []ConsultationAssignment      `gorm:"foreignKey:ModuleID" json:"assignments,omitempty"`
	EligibilityRules []ConsultationEligibilityRule `gorm:"foreignKey:ModuleID" json:"eligibility_rules,omitempty"`
	Appointments     []ConsultationAppointment     `gorm:"foreignKey:ModuleID" json:"appointments,omitempty"`
}

// ConsultationAssignment 作業配置實體
type ConsultationAssignment struct {
	ID             uint      `gorm:"primaryKey" json:"id"`
	ModuleID       uint      `gorm:"not null" json:"module_id"`
	Title          string    `gorm:"not null" json:"title"`
	Description    string    `json:"description"`
	RequiredFields string    `gorm:"type:json" json:"required_fields"` // JSON 格式的必填欄位配置
	DeadlineHours  int       `gorm:"default:72" json:"deadline_hours"`
	IsRequired     bool      `gorm:"default:true" json:"is_required"`
	SortOrder      int       `gorm:"default:0" json:"sort_order"`
	CreatedByID    uint      `gorm:"not null" json:"created_by_id"`
	UpdatedByID    null.Int  `json:"updated_by_id"`
	CreatedAt      time.Time `json:"created_at"`
	UpdatedAt      time.Time `json:"updated_at"`

	// 關聯
	Module      ConsultationModule           `gorm:"foreignKey:ModuleID" json:"module,omitempty"`
	Submissions []MemberAssignmentSubmission `gorm:"foreignKey:AssignmentID" json:"submissions,omitempty"`
}

// MemberAssignmentSubmission 會員作業提交實體
type MemberAssignmentSubmission struct {
	ID             uint                       `gorm:"primaryKey" json:"id"`
	MemberID       uint                       `gorm:"not null" json:"member_id"`
	AssignmentID   uint                       `gorm:"not null" json:"assignment_id"`
	SubmissionData string                     `gorm:"type:json" json:"submission_data"` // JSON 格式的提交內容
	Files          string                     `gorm:"type:json" json:"files"`           // JSON 格式的檔案資訊
	Status         AssignmentSubmissionStatus `gorm:"type:enum('draft','submitted','approved','rejected');default:'draft'" json:"status"`
	SubmittedAt    null.Time                  `json:"submitted_at"`
	ReviewedAt     null.Time                  `json:"reviewed_at"`
	ReviewedByID   null.Int                   `json:"reviewed_by_id"`
	ReviewNote     string                     `json:"review_note"`
	CreatedAt      time.Time                  `json:"created_at"`
	UpdatedAt      time.Time                  `json:"updated_at"`

	// 關聯
	Member     Member                 `gorm:"foreignKey:MemberID" json:"member,omitempty"`
	Assignment ConsultationAssignment `gorm:"foreignKey:AssignmentID" json:"assignment,omitempty"`
	ReviewedBy Admin                  `gorm:"foreignKey:ReviewedByID" json:"reviewed_by,omitempty"`
}

// ConsultationAppointment 新版預約實體
type ConsultationAppointment struct {
	ID                    uint                          `gorm:"primaryKey" json:"id"`
	MemberID              uint                          `gorm:"not null" json:"member_id"`
	ModuleID              uint                          `gorm:"not null" json:"module_id"`
	SubmissionID          null.Int                      `json:"submission_id"`
	Title                 string                        `gorm:"not null" json:"title"`
	Description           string                        `json:"description"`
	AppointmentDatetime   time.Time                     `gorm:"not null" json:"appointment_datetime"`
	DurationMinutes       int                           `gorm:"default:30" json:"duration_minutes"`
	Status                ConsultationAppointmentStatus `gorm:"type:enum('pending','confirmed','completed','cancelled');default:'pending'" json:"status"`
	ZoomMeetingID         string                        `json:"zoom_meeting_id"`
	ZoomJoinURL           string                        `json:"zoom_join_url"`
	GoogleCalendarEventID string                        `json:"google_calendar_event_id"`
	Notes                 string                        `json:"notes"`
	CreatedByID           null.Int                      `json:"created_by_id"`
	UpdatedByID           null.Int                      `json:"updated_by_id"`
	CreatedAt             time.Time                     `json:"created_at"`
	UpdatedAt             time.Time                     `json:"updated_at"`

	// 關聯
	Member     Member                     `gorm:"foreignKey:MemberID" json:"member,omitempty"`
	Module     ConsultationModule         `gorm:"foreignKey:ModuleID" json:"module,omitempty"`
	Submission MemberAssignmentSubmission `gorm:"foreignKey:SubmissionID" json:"submission,omitempty"`
}

// ConsultationTimeSlot 時段管理實體
type ConsultationTimeSlot struct {
	ID              uint      `gorm:"primaryKey" json:"id"`
	Date            time.Time `gorm:"type:date;not null" json:"date"`
	StartTime       time.Time `gorm:"type:time;not null" json:"start_time"`
	EndTime         time.Time `gorm:"type:time;not null" json:"end_time"`
	IsAvailable     bool      `gorm:"default:true" json:"is_available"`
	MaxAppointments int       `gorm:"default:1" json:"max_appointments"`
	CreatedByID     uint      `gorm:"not null" json:"created_by_id"`
	UpdatedByID     null.Int  `json:"updated_by_id"`
	CreatedAt       time.Time `json:"created_at"`
	UpdatedAt       time.Time `json:"updated_at"`
}

// ConsultationEligibilityRule 條件規則實體
type ConsultationEligibilityRule struct {
	ID          uint      `gorm:"primaryKey" json:"id"`
	ModuleID    uint      `gorm:"not null" json:"module_id"`
	RuleName    string    `gorm:"not null" json:"rule_name"`
	Conditions  string    `gorm:"type:json;not null" json:"conditions"` // JSON 格式的條件配置
	IsActive    bool      `gorm:"default:true" json:"is_active"`
	CreatedByID uint      `gorm:"not null" json:"created_by_id"`
	UpdatedByID null.Int  `json:"updated_by_id"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`

	// 關聯
	Module ConsultationModule `gorm:"foreignKey:ModuleID" json:"module,omitempty"`
}

// TableName 設定表名
func (ConsultationModule) TableName() string {
	return "consultation_modules"
}

func (ConsultationAssignment) TableName() string {
	return "consultation_assignments"
}

func (MemberAssignmentSubmission) TableName() string {
	return "member_assignment_submissions"
}

func (ConsultationAppointment) TableName() string {
	return "consultation_appointments"
}

func (ConsultationTimeSlot) TableName() string {
	return "consultation_time_slots"
}

func (ConsultationEligibilityRule) TableName() string {
	return "consultation_eligibility_rules"
}
