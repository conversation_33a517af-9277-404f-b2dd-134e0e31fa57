{{define "admin/consultation_v2/modules"}}
<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>諮詢模組管理 - G.R.A.C.E 優雅學院後台</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .module-card {
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            background: #fff;
            transition: all 0.2s ease;
        }
        .module-card:hover {
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .module-status {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
        }
        .status-active { background: #d4edda; color: #155724; }
        .status-inactive { background: #f8d7da; color: #721c24; }
        .category-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: bold;
            background: #e3f2fd;
            color: #0d47a1;
        }
        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .filter-section {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container-fluid mt-4">
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2><i class="fas fa-puzzle-piece"></i> 諮詢模組管理</h2>
                    <div>
                        <button class="btn btn-success" data-bs-toggle="modal" data-bs-target="#moduleModal" onclick="openCreateModal()">
                            <i class="fas fa-plus"></i> 新增模組
                        </button>
                        <a href="/admin/consultations/v2" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left"></i> 返回總覽
                        </a>
                    </div>
                </div>

                <!-- 統計卡片 -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="stats-card">
                            <h5><i class="fas fa-puzzle-piece"></i> 總模組數</h5>
                            <h2 id="totalModules">0</h2>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stats-card">
                            <h5><i class="fas fa-check-circle"></i> 啟用模組</h5>
                            <h2 id="activeModules">0</h2>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stats-card">
                            <h5><i class="fas fa-calendar-check"></i> 本月預約</h5>
                            <h2 id="monthlyAppointments">0</h2>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stats-card">
                            <h5><i class="fas fa-file-alt"></i> 待審核作業</h5>
                            <h2 id="pendingAssignments">0</h2>
                        </div>
                    </div>
                </div>

                <!-- 篩選區域 -->
                <div class="filter-section">
                    <div class="row">
                        <div class="col-md-3">
                            <label class="form-label">分類篩選</label>
                            <select class="form-select" id="categoryFilter" onchange="filterModules()">
                                <option value="">全部分類</option>
                                <option value="basic_guidance">基礎指導</option>
                                <option value="advanced_guidance">進階指導</option>
                                <option value="specialized_consultation">專業諮詢</option>
                                <option value="group_session">團體課程</option>
                                <option value="activity_gift">活動禮品</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">狀態篩選</label>
                            <select class="form-select" id="statusFilter" onchange="filterModules()">
                                <option value="">全部狀態</option>
                                <option value="true">啟用</option>
                                <option value="false">停用</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">搜尋</label>
                            <input type="text" class="form-control" id="searchInput" placeholder="搜尋模組名稱或描述..." onkeyup="filterModules()">
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">&nbsp;</label>
                            <button class="btn btn-outline-secondary w-100" onclick="resetFilters()">
                                <i class="fas fa-undo"></i> 重置
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 模組列表 -->
                <div id="modulesList">
                    <!-- 模組卡片將由 JavaScript 動態載入 -->
                </div>

                <!-- 分頁 -->
                <nav aria-label="模組分頁" id="pagination" style="display: none;">
                    <ul class="pagination justify-content-center">
                        <!-- 分頁將由 JavaScript 生成 -->
                    </ul>
                </nav>
            </div>
        </div>
    </div>

    <!-- 模組編輯模態框 -->
    <div class="modal fade" id="moduleModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="modalTitle">新增模組</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="moduleForm">
                        <input type="hidden" id="moduleId">
                        <div class="row">
                            <div class="col-md-8">
                                <div class="mb-3">
                                    <label for="moduleName" class="form-label">模組名稱 *</label>
                                    <input type="text" class="form-control" id="moduleName" required>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="sortOrder" class="form-label">排序順序</label>
                                    <input type="number" class="form-control" id="sortOrder" value="1" min="1">
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="category" class="form-label">分類 *</label>
                                    <select class="form-select" id="category" required>
                                        <option value="">請選擇分類</option>
                                        <option value="basic_guidance">基礎指導</option>
                                        <option value="advanced_guidance">進階指導</option>
                                        <option value="specialized_consultation">專業諮詢</option>
                                        <option value="group_session">團體課程</option>
                                        <option value="activity_gift">活動禮品</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="isActive" class="form-label">狀態</label>
                                    <select class="form-select" id="isActive">
                                        <option value="true">啟用</option>
                                        <option value="false">停用</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="description" class="form-label">模組描述 *</label>
                            <textarea class="form-control" id="description" rows="4" required 
                                      placeholder="請詳細描述此模組的內容、目標和適用對象..."></textarea>
                        </div>

                        <div class="mb-3">
                            <label for="requirements" class="form-label">參與要求</label>
                            <textarea class="form-control" id="requirements" rows="3" 
                                      placeholder="參與此模組需要滿足的條件或要求..."></textarea>
                        </div>

                        <div class="mb-3">
                            <label for="objectives" class="form-label">學習目標</label>
                            <textarea class="form-control" id="objectives" rows="3" 
                                      placeholder="完成此模組後學員將獲得的知識或技能..."></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="saveModule()">儲存</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 刪除確認模態框 -->
    <div class="modal fade" id="deleteModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">確認刪除</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>確定要刪除模組「<span id="deleteModuleName"></span>」嗎？</p>
                    <p class="text-danger small">此操作無法復原，相關的作業和預約記錄也會受到影響。</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-danger" id="confirmDeleteBtn">確認刪除</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let modules = [];
        let filteredModules = [];
        let currentPage = 1;
        let totalPages = 1;
        let deleteModal, moduleModal;

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
            moduleModal = new bootstrap.Modal(document.getElementById('moduleModal'));
            
            loadModules();
            loadStatistics();
        });

        // 載入模組列表
        function loadModules() {
            fetch('/api/admin/v2/modules')
            .then(response => response.json())
            .then(data => {
                if (data.msg === '取得模組列表成功') {
                    modules = data.data;
                    filteredModules = [...modules];
                    renderModules();
                    updateStatistics();
                } else {
                    console.error('載入模組失敗:', data.error);
                }
            })
            .catch(error => {
                console.error('載入模組失敗:', error);
            });
        }

        // 載入統計資料
        function loadStatistics() {
            fetch('/api/admin/v2/statistics')
            .then(response => response.json())
            .then(data => {
                if (data.msg === '取得統計資料成功') {
                    const stats = data.data;
                    document.getElementById('totalModules').textContent = stats.total_modules || 0;
                    document.getElementById('activeModules').textContent = stats.active_modules || 0;
                    document.getElementById('monthlyAppointments').textContent = stats.monthly_appointments || 0;
                    document.getElementById('pendingAssignments').textContent = stats.pending_assignments || 0;
                }
            })
            .catch(error => {
                console.error('載入統計資料失敗:', error);
            });
        }

        // 渲染模組列表
        function renderModules() {
            const container = document.getElementById('modulesList');
            
            if (filteredModules.length === 0) {
                container.innerHTML = `
                    <div class="text-center py-5">
                        <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                        <h5>暫無模組</h5>
                        <p class="text-muted">點擊上方「新增模組」按鈕開始建立第一個模組</p>
                    </div>
                `;
                return;
            }

            container.innerHTML = filteredModules.map(module => `
                <div class="module-card">
                    <div class="d-flex justify-content-between align-items-start mb-3">
                        <div>
                            <h5>${module.name}</h5>
                            <div class="mb-2">
                                <span class="category-badge">${getCategoryText(module.category)}</span>
                                <span class="module-status status-${module.is_active ? 'active' : 'inactive'} ms-2">
                                    ${module.is_active ? '啟用' : '停用'}
                                </span>
                            </div>
                        </div>
                        <div class="dropdown">
                            <button class="btn btn-outline-secondary btn-sm dropdown-toggle" data-bs-toggle="dropdown">
                                <i class="fas fa-cog"></i>
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="#" onclick="editModule(${module.id})">
                                    <i class="fas fa-edit"></i> 編輯
                                </a></li>
                                <li><a class="dropdown-item" href="/admin/consultations/v2/modules/${module.id}/assignments">
                                    <i class="fas fa-tasks"></i> 管理作業
                                </a></li>
                                <li><a class="dropdown-item" href="/admin/consultations/v2/modules/${module.id}/rules">
                                    <i class="fas fa-shield-alt"></i> 資格規則
                                </a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item text-danger" href="#" onclick="deleteModule(${module.id}, '${module.name}')">
                                    <i class="fas fa-trash"></i> 刪除
                                </a></li>
                            </ul>
                        </div>
                    </div>
                    
                    <p class="text-muted mb-3">${module.description}</p>
                    
                    <div class="row">
                        <div class="col-md-4">
                            <small class="text-muted">作業數量</small>
                            <div><strong>${module.assignment_count || 0}</strong></div>
                        </div>
                        <div class="col-md-4">
                            <small class="text-muted">總預約數</small>
                            <div><strong>${module.appointment_count || 0}</strong></div>
                        </div>
                        <div class="col-md-4">
                            <small class="text-muted">排序順序</small>
                            <div><strong>${module.sort_order}</strong></div>
                        </div>
                    </div>
                </div>
            `).join('');
        }

        // 取得分類文字
        function getCategoryText(category) {
            const categoryMap = {
                'basic_guidance': '基礎指導',
                'advanced_guidance': '進階指導',
                'specialized_consultation': '專業諮詢',
                'group_session': '團體課程',
                'activity_gift': '活動禮品'
            };
            return categoryMap[category] || category;
        }

        // 篩選模組
        function filterModules() {
            const categoryFilter = document.getElementById('categoryFilter').value;
            const statusFilter = document.getElementById('statusFilter').value;
            const searchInput = document.getElementById('searchInput').value.toLowerCase();

            filteredModules = modules.filter(module => {
                const matchCategory = !categoryFilter || module.category === categoryFilter;
                const matchStatus = !statusFilter || module.is_active.toString() === statusFilter;
                const matchSearch = !searchInput || 
                    module.name.toLowerCase().includes(searchInput) ||
                    module.description.toLowerCase().includes(searchInput);

                return matchCategory && matchStatus && matchSearch;
            });

            renderModules();
        }

        // 重置篩選
        function resetFilters() {
            document.getElementById('categoryFilter').value = '';
            document.getElementById('statusFilter').value = '';
            document.getElementById('searchInput').value = '';
            filteredModules = [...modules];
            renderModules();
        }

        // 開啟新增模態框
        function openCreateModal() {
            document.getElementById('modalTitle').textContent = '新增模組';
            document.getElementById('moduleForm').reset();
            document.getElementById('moduleId').value = '';
            document.getElementById('isActive').value = 'true';
            document.getElementById('sortOrder').value = modules.length + 1;
        }

        // 編輯模組
        function editModule(moduleId) {
            const module = modules.find(m => m.id === moduleId);
            if (!module) return;

            document.getElementById('modalTitle').textContent = '編輯模組';
            document.getElementById('moduleId').value = module.id;
            document.getElementById('moduleName').value = module.name;
            document.getElementById('category').value = module.category;
            document.getElementById('description').value = module.description;
            document.getElementById('requirements').value = module.requirements || '';
            document.getElementById('objectives').value = module.objectives || '';
            document.getElementById('isActive').value = module.is_active.toString();
            document.getElementById('sortOrder').value = module.sort_order;

            moduleModal.show();
        }

        // 儲存模組
        function saveModule() {
            const moduleId = document.getElementById('moduleId').value;
            const isEdit = !!moduleId;

            const moduleData = {
                name: document.getElementById('moduleName').value,
                category: document.getElementById('category').value,
                description: document.getElementById('description').value,
                requirements: document.getElementById('requirements').value,
                objectives: document.getElementById('objectives').value,
                is_active: document.getElementById('isActive').value === 'true',
                sort_order: parseInt(document.getElementById('sortOrder').value)
            };

            const url = isEdit ? `/api/admin/v2/modules/${moduleId}` : '/api/admin/v2/modules';
            const method = isEdit ? 'PUT' : 'POST';

            fetch(url, {
                method: method,
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(moduleData)
            })
            .then(response => response.json())
            .then(data => {
                if (data.msg === (isEdit ? '更新模組成功' : '建立模組成功')) {
                    moduleModal.hide();
                    loadModules();
                    alert(isEdit ? '模組更新成功！' : '模組建立成功！');
                } else {
                    alert('操作失敗: ' + data.error);
                }
            })
            .catch(error => {
                alert('操作失敗: ' + error.message);
            });
        }

        // 刪除模組
        function deleteModule(moduleId, moduleName) {
            document.getElementById('deleteModuleName').textContent = moduleName;
            document.getElementById('confirmDeleteBtn').onclick = function() {
                fetch(`/api/admin/v2/modules/${moduleId}`, {
                    method: 'DELETE'
                })
                .then(response => response.json())
                .then(data => {
                    if (data.msg === '刪除模組成功') {
                        deleteModal.hide();
                        loadModules();
                        alert('模組刪除成功！');
                    } else {
                        alert('刪除失敗: ' + data.error);
                    }
                })
                .catch(error => {
                    alert('刪除失敗: ' + error.message);
                });
            };
            deleteModal.show();
        }

        // 更新統計
        function updateStatistics() {
            const totalModules = modules.length;
            const activeModules = modules.filter(m => m.is_active).length;
            
            document.getElementById('totalModules').textContent = totalModules;
            document.getElementById('activeModules').textContent = activeModules;
        }
    </script>
</body>
</html>
{{end}}
