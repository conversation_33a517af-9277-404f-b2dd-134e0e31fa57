{{define "consultation_v2.modules.tmpl"}}
<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{.title}} - G.R.A.C.E 優雅學院</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .module-card {
            transition: transform 0.2s ease-in-out;
            border: none;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .module-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 4px 20px rgba(0,0,0,0.15);
        }
        .category-badge {
            font-size: 0.8rem;
            padding: 0.25rem 0.5rem;
        }
        .btn-consultation {
            background: linear-gradient(45deg, #667eea 0%, #764ba2 100%);
            border: none;
            color: white;
        }
        .btn-consultation:hover {
            background: linear-gradient(45deg, #5a6fd8 0%, #6a4190 100%);
            color: white;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2><i class="fas fa-comments me-2"></i>諮詢模組</h2>
                    <div class="btn-group" role="group">
                        <input type="radio" class="btn-check" name="category" id="all" value="" checked>
                        <label class="btn btn-outline-primary" for="all">全部</label>
                        
                        <input type="radio" class="btn-check" name="category" id="advanced" value="advanced_guidance">
                        <label class="btn btn-outline-primary" for="advanced">進階指導</label>
                        
                        <input type="radio" class="btn-check" name="category" id="activity" value="activity_gift">
                        <label class="btn btn-outline-primary" for="activity">活動禮品</label>
                    </div>
                </div>
            </div>
        </div>

        <div class="row" id="modules-container">
            <!-- 模組卡片將通過 JavaScript 動態載入 -->
        </div>

        <!-- 載入中指示器 -->
        <div class="text-center py-5" id="loading">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">載入中...</span>
            </div>
            <p class="mt-2">載入模組中...</p>
        </div>

        <!-- 空狀態 -->
        <div class="text-center py-5 d-none" id="empty-state">
            <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
            <h4 class="text-muted">目前沒有可用的諮詢模組</h4>
            <p class="text-muted">請稍後再試或聯繫管理員</p>
        </div>
    </div>

    <!-- 模組詳情模態框 -->
    <div class="modal fade" id="moduleModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="moduleModalTitle"></h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="moduleModalBody">
                    <!-- 模組詳情內容 -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">關閉</button>
                    <button type="button" class="btn btn-consultation" id="startConsultationBtn">
                        <i class="fas fa-play me-2"></i>開始諮詢
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <script>
        let currentModules = [];
        let selectedModule = null;

        // 載入模組列表
        async function loadModules(category = '') {
            try {
                document.getElementById('loading').classList.remove('d-none');
                document.getElementById('empty-state').classList.add('d-none');
                
                const params = category ? { category } : {};
                const response = await axios.get('/api/v2/consultations/modules', { params });
                
                currentModules = response.data.data || [];
                renderModules(currentModules);
                
            } catch (error) {
                console.error('載入模組失敗:', error);
                showError('載入模組失敗，請稍後再試');
            } finally {
                document.getElementById('loading').classList.add('d-none');
            }
        }

        // 渲染模組卡片
        function renderModules(modules) {
            const container = document.getElementById('modules-container');
            
            if (modules.length === 0) {
                container.innerHTML = '';
                document.getElementById('empty-state').classList.remove('d-none');
                return;
            }

            const html = modules.map(module => `
                <div class="col-md-6 col-lg-4 mb-4">
                    <div class="card module-card h-100">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-start mb-3">
                                <h5 class="card-title">${module.name}</h5>
                                <span class="badge category-badge ${getCategoryClass(module.category)}">
                                    ${getCategoryName(module.category)}
                                </span>
                            </div>
                            <p class="card-text text-muted">${module.description || '暫無描述'}</p>
                        </div>
                        <div class="card-footer bg-transparent">
                            <button class="btn btn-consultation w-100" onclick="showModuleDetails(${module.id})">
                                <i class="fas fa-info-circle me-2"></i>查看詳情
                            </button>
                        </div>
                    </div>
                </div>
            `).join('');

            container.innerHTML = html;
        }

        // 顯示模組詳情
        async function showModuleDetails(moduleId) {
            try {
                selectedModule = currentModules.find(m => m.id === moduleId);
                if (!selectedModule) return;

                // 檢查預約資格
                const eligibilityResponse = await axios.get('/api/v2/consultations/check-eligibility', {
                    params: { module_id: moduleId }
                });

                const eligible = eligibilityResponse.data.data.eligible;
                const reasons = eligibilityResponse.data.data.reasons || [];

                // 載入作業要求
                const assignmentsResponse = await axios.get(`/api/v2/consultations/${moduleId}/assignments`);
                const assignments = assignmentsResponse.data.data || [];

                // 更新模態框內容
                document.getElementById('moduleModalTitle').textContent = selectedModule.name;
                document.getElementById('moduleModalBody').innerHTML = `
                    <div class="mb-4">
                        <h6>模組描述</h6>
                        <p>${selectedModule.description || '暫無描述'}</p>
                    </div>

                    ${assignments.length > 0 ? `
                        <div class="mb-4">
                            <h6>作業要求</h6>
                            <div class="list-group">
                                ${assignments.map(assignment => `
                                    <div class="list-group-item">
                                        <h6 class="mb-1">${assignment.title}</h6>
                                        <p class="mb-1 text-muted">${assignment.description || ''}</p>
                                        ${assignment.is_required ? '<small class="text-danger">必須完成</small>' : '<small class="text-muted">選擇性完成</small>'}
                                    </div>
                                `).join('')}
                            </div>
                        </div>
                    ` : ''}

                    <div class="mb-3">
                        <h6>預約資格</h6>
                        ${eligible ? 
                            '<div class="alert alert-success"><i class="fas fa-check-circle me-2"></i>您符合預約資格</div>' :
                            `<div class="alert alert-warning">
                                <i class="fas fa-exclamation-triangle me-2"></i>暫不符合預約資格
                                <ul class="mb-0 mt-2">
                                    ${reasons.map(reason => `<li>${reason}</li>`).join('')}
                                </ul>
                            </div>`
                        }
                    </div>
                `;

                // 設置開始諮詢按鈕
                const startBtn = document.getElementById('startConsultationBtn');
                if (eligible) {
                    startBtn.disabled = false;
                    startBtn.onclick = () => startConsultation(moduleId);
                } else {
                    startBtn.disabled = true;
                }

                // 顯示模態框
                new bootstrap.Modal(document.getElementById('moduleModal')).show();

            } catch (error) {
                console.error('載入模組詳情失敗:', error);
                showError('載入模組詳情失敗');
            }
        }

        // 開始諮詢流程
        function startConsultation(moduleId) {
            // 關閉模態框
            bootstrap.Modal.getInstance(document.getElementById('moduleModal')).hide();
            
            // 跳轉到作業提交頁面
            window.location.href = `/consultations/v2/modules/${moduleId}/assignments`;
        }

        // 獲取分類樣式
        function getCategoryClass(category) {
            switch (category) {
                case 'basic_guidance': return 'bg-info';
                case 'advanced_guidance': return 'bg-primary';
                case 'specialized_consultation': return 'bg-warning';
                case 'group_session': return 'bg-success';
                case 'activity_gift': return 'bg-secondary';
                default: return 'bg-secondary';
            }
        }

        // 獲取分類名稱
        function getCategoryName(category) {
            switch (category) {
                case 'basic_guidance': return '基礎指導';
                case 'advanced_guidance': return '進階指導';
                case 'specialized_consultation': return '專業諮詢';
                case 'group_session': return '團體課程';
                case 'activity_gift': return '活動禮品';
                default: return '其他';
            }
        }

        // 顯示錯誤訊息
        function showError(message) {
            // 這裡可以整合現有的錯誤提示系統
            alert(message);
        }

        // 分類篩選事件
        document.querySelectorAll('input[name="category"]').forEach(radio => {
            radio.addEventListener('change', (e) => {
                loadModules(e.target.value);
            });
        });

        // 頁面載入時初始化
        document.addEventListener('DOMContentLoaded', () => {
            loadModules();
        });
    </script>
</body>
</html>
{{end}}
