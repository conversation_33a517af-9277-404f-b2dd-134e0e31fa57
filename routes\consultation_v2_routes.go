package routes

import (
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"

	"cx/app/controllers"
	"cx/app/middleware"
)

// SetupConsultationV2Routes 設置新版諮詢系統路由
func SetupConsultationV2Routes(router *gin.Engine, db *gorm.DB) {
	// 建立控制器
	consultationController := controllers.NewConsultationV2Controller(db)
	adminConsultationController := controllers.NewAdminConsultationV2Controller(db)

	// 前台 API 路由 (會員端)
	frontAPI := router.Group("/api/v2")
	{
		// 諮詢模組相關
		consultations := frontAPI.Group("/consultations")
		{
			consultations.GET("/modules", consultationController.GetModules)
			consultations.GET("/:id/assignments", consultationController.GetModuleAssignments)
			consultations.GET("/check-eligibility", middleware.RequireMemberLogin("api", ""), consultationController.CheckEligibility)
		}

		// 作業相關
		assignments := frontAPI.Group("/assignments")
		assignments.Use(middleware.RequireMemberLogin("api", ""))
		{
			assignments.POST("/submit", consultationController.SubmitAssignment)
		}

		// 預約相關
		appointments := frontAPI.Group("/appointments")
		{
			appointments.GET("/available-slots", consultationController.GetAvailableSlots)
		}

		// 需要會員認證的路由
		authAppointments := appointments.Group("")
		authAppointments.Use(middleware.RequireMemberLogin("api", ""))
		{
			authAppointments.POST("", consultationController.BookAppointment)
			authAppointments.GET("", consultationController.GetMyAppointments)
			authAppointments.PATCH("/:id/cancel", consultationController.CancelAppointment)
		}

		// 檔案上傳相關
		files := frontAPI.Group("/files")
		files.Use(middleware.RequireMemberLogin("api", ""))
		{
			fileController := controllers.NewFileUploadController(db, "./uploads", 10*1024*1024) // 10MB
			files.POST("/upload", fileController.UploadFile)
			files.GET("/config", fileController.GetUploadConfig)
			files.GET("/:id", fileController.GetFile)
			files.GET("", fileController.GetMyFiles)
			files.DELETE("/:id", fileController.DeleteFile)
			files.GET("/:id/download", fileController.DownloadFile)
		}
	}

	// 後台 API 路由 (管理端)
	adminAPI := router.Group("/api/v2/admin")
	adminAPI.Use(middleware.RequireAdminLogin("api"))
	{
		// 諮詢模組管理
		modules := adminAPI.Group("/consultations/modules")
		{
			modules.GET("", adminConsultationController.GetModules)
			modules.POST("", adminConsultationController.CreateModule)
			modules.PATCH("/:id", adminConsultationController.UpdateModule)
			modules.DELETE("/:id", adminConsultationController.DeleteModule)
		}

		// 作業配置管理
		assignments := adminAPI.Group("/assignments")
		{
			assignments.GET("", adminConsultationController.GetAssignments)
			assignments.POST("", adminConsultationController.CreateAssignment)
		}

		// 作業提交管理
		submissions := adminAPI.Group("/submissions")
		{
			submissions.GET("", adminConsultationController.GetSubmissions)
			submissions.PATCH("/:id/review", adminConsultationController.ReviewSubmission)
		}

		// 時段管理
		timeSlots := adminAPI.Group("/time-slots")
		{
			timeSlots.GET("", adminConsultationController.GetTimeSlots)
			timeSlots.POST("", adminConsultationController.CreateTimeSlot)
			timeSlots.PATCH("/:id", adminConsultationController.UpdateTimeSlot)
			timeSlots.DELETE("/:id", adminConsultationController.DeleteTimeSlot)
		}

		// 預約管理
		appointments := adminAPI.Group("/appointments")
		{
			appointments.GET("", adminConsultationController.GetAppointments)
			appointments.PATCH("/:id/confirm", adminConsultationController.ConfirmAppointment)
			appointments.PATCH("/:id/cancel", adminConsultationController.CancelAppointment)
		}
	}

	// 前台頁面路由
	frontPages := router.Group("/consultations/v2")
	frontPages.Use(middleware.RequireMemberLogin("page", "/consultations/v2"))
	{
		pageController := controllers.NewConsultationV2PageController(db)

		// 模組相關頁面
		frontPages.GET("", pageController.ShowModules)
		frontPages.GET("/modules", pageController.ShowModules)
		frontPages.GET("/modules/:id", pageController.ShowModuleDetail)
		frontPages.GET("/modules/:id/assignments", pageController.ShowAssignmentList)
		frontPages.GET("/assignments/:id/submit", pageController.ShowAssignmentSubmit)

		// 預約相關頁面
		frontPages.GET("/book", pageController.ShowAppointmentBooking)
		frontPages.GET("/book/:module_id", pageController.ShowAppointmentBooking)
		frontPages.GET("/appointments", pageController.ShowMyAppointments)
		frontPages.GET("/appointments/:id", pageController.ShowAppointmentDetail)
	}

	// 後台頁面路由
	adminPages := router.Group("/admin/consultations/v2")
	adminPages.Use(middleware.RequireAdminLogin("page"))
	{
		adminPageController := controllers.NewAdminConsultationV2PageController(db)

		// 主要管理頁面
		adminPages.GET("", adminPageController.ShowDashboard)
		adminPages.GET("/modules", adminPageController.ShowModules)
		adminPages.GET("/appointments", adminPageController.ShowAppointments)
		adminPages.GET("/time-slots", adminPageController.ShowTimeSlots)
		adminPages.GET("/assignments", adminPageController.ShowAssignmentSubmissions)
		adminPages.GET("/statistics", adminPageController.ShowStatistics)
		adminPages.GET("/settings", adminPageController.ShowSettings)

		// 詳情頁面
		adminPages.GET("/modules/:id/assignments", adminPageController.ShowModuleAssignments)
		adminPages.GET("/modules/:id/rules", adminPageController.ShowModuleRules)
		adminPages.GET("/appointments/:id", adminPageController.ShowAppointmentDetail)
		adminPages.GET("/members/:id", adminPageController.ShowMemberDetail)

		// 報表和工具
		adminPages.GET("/reports", adminPageController.ShowReports)
		adminPages.GET("/export", adminPageController.ShowExportData)
		adminPages.GET("/logs", adminPageController.ShowSystemLogs)
		adminPages.GET("/backup", adminPageController.ShowBackup)
		adminPages.GET("/migration", adminPageController.ShowMigration)

		// 系統管理
		adminPages.GET("/notifications", adminPageController.ShowNotifications)
		adminPages.GET("/email-templates", adminPageController.ShowEmailTemplates)
		adminPages.GET("/integrations", adminPageController.ShowIntegrations)
		adminPages.GET("/permissions", adminPageController.ShowPermissions)
		adminPages.GET("/audit-logs", adminPageController.ShowAuditLogs)
	}

	// 後台 API 路由
	adminAPI := router.Group("/api/admin/v2")
	adminAPI.Use(middleware.RequireAdminLogin("api"))
	{
		adminController := controllers.NewAdminConsultationV2Controller(db)

		// 總覽
		adminAPI.GET("/dashboard", adminController.GetDashboard)

		// 模組管理
		adminAPI.GET("/modules", adminController.GetModules)
		adminAPI.POST("/modules", adminController.CreateModule)
		adminAPI.PUT("/modules/:id", adminController.UpdateModule)
		adminAPI.DELETE("/modules/:id", adminController.DeleteModule)

		// 模組管理 API（使用現有的方法）
		adminAPI.GET("/modules", adminController.GetModules)
		adminAPI.POST("/modules", adminController.CreateModule)
		adminAPI.PUT("/modules/:id", adminController.UpdateModule)
		adminAPI.DELETE("/modules/:id", adminController.DeleteModule)

		// 預約管理 API（使用現有的方法）
		adminAPI.GET("/appointments", adminController.GetAppointments)
		adminAPI.PATCH("/appointments/:id/confirm", adminController.ConfirmAppointment)
		adminAPI.PATCH("/appointments/:id/cancel", adminController.CancelAppointment)

		// 作業審核 API（使用現有的方法）
		adminAPI.GET("/submissions", adminController.GetSubmissions)
		adminAPI.PATCH("/submissions/:id/review", adminController.ReviewSubmission)

		// 統計 API（暫時註解，待實現）
		// adminAPI.GET("/statistics", adminController.GetStatistics)
	}
}
