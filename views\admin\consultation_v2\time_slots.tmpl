{{define "admin/consultation_v2/time_slots"}}
<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>時段管理 - G.R.A.C.E 優雅學院後台</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .calendar-container {
            background: #fff;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .calendar-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }
        .calendar-grid {
            display: grid;
            grid-template-columns: repeat(7, 1fr);
            gap: 1px;
            background: #e0e0e0;
            border: 1px solid #e0e0e0;
        }
        .calendar-day {
            background: #fff;
            padding: 10px;
            text-align: center;
            cursor: pointer;
            transition: all 0.2s ease;
            min-height: 80px;
            display: flex;
            flex-direction: column;
            justify-content: flex-start;
        }
        .calendar-day:hover {
            background: #f0f8ff;
        }
        .calendar-day.disabled {
            background: #f5f5f5;
            color: #ccc;
            cursor: not-allowed;
        }
        .calendar-day.selected {
            background: #007bff;
            color: white;
        }
        .calendar-day.has-slots {
            background: #e8f5e8;
        }
        .slot-indicator {
            font-size: 10px;
            margin-top: 5px;
            padding: 2px 4px;
            border-radius: 3px;
            background: #28a745;
            color: white;
        }
        .slot-card {
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
            background: #fff;
            transition: all 0.2s ease;
        }
        .slot-card:hover {
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .slot-status {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
        }
        .status-available { background: #d4edda; color: #155724; }
        .status-full { background: #f8d7da; color: #721c24; }
        .status-disabled { background: #e2e3e5; color: #6c757d; }
        .quick-actions {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container-fluid mt-4">
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2><i class="fas fa-clock"></i> 時段管理</h2>
                    <div>
                        <button class="btn btn-success" data-bs-toggle="modal" data-bs-target="#slotModal" onclick="openCreateModal()">
                            <i class="fas fa-plus"></i> 新增時段
                        </button>
                        <button class="btn btn-primary" onclick="batchCreateSlots()">
                            <i class="fas fa-layer-group"></i> 批量建立
                        </button>
                        <a href="/admin/consultations/v2" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left"></i> 返回總覽
                        </a>
                    </div>
                </div>

                <!-- 統計卡片 -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="stats-card">
                            <h5><i class="fas fa-clock"></i> 總時段數</h5>
                            <h2 id="totalSlots">0</h2>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stats-card">
                            <h5><i class="fas fa-check-circle"></i> 可用時段</h5>
                            <h2 id="availableSlots">0</h2>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stats-card">
                            <h5><i class="fas fa-calendar-day"></i> 今日時段</h5>
                            <h2 id="todaySlots">0</h2>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stats-card">
                            <h5><i class="fas fa-users"></i> 已預約</h5>
                            <h2 id="bookedSlots">0</h2>
                        </div>
                    </div>
                </div>

                <!-- 快速操作 -->
                <div class="quick-actions">
                    <h6><i class="fas fa-bolt"></i> 快速操作</h6>
                    <div class="row">
                        <div class="col-md-3">
                            <button class="btn btn-outline-primary w-100" onclick="createWeeklySlots()">
                                <i class="fas fa-calendar-week"></i> 建立本週時段
                            </button>
                        </div>
                        <div class="col-md-3">
                            <button class="btn btn-outline-success w-100" onclick="createMonthlySlots()">
                                <i class="fas fa-calendar"></i> 建立本月時段
                            </button>
                        </div>
                        <div class="col-md-3">
                            <button class="btn btn-outline-warning w-100" onclick="copyPreviousWeek()">
                                <i class="fas fa-copy"></i> 複製上週時段
                            </button>
                        </div>
                        <div class="col-md-3">
                            <button class="btn btn-outline-danger w-100" onclick="clearFutureSlots()">
                                <i class="fas fa-trash"></i> 清除未來時段
                            </button>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <!-- 日曆視圖 -->
                    <div class="col-md-8">
                        <div class="calendar-container">
                            <div class="calendar-header">
                                <button class="btn btn-outline-secondary" id="prevMonth">
                                    <i class="fas fa-chevron-left"></i>
                                </button>
                                <h5 id="currentMonth"></h5>
                                <button class="btn btn-outline-secondary" id="nextMonth">
                                    <i class="fas fa-chevron-right"></i>
                                </button>
                            </div>
                            
                            <div class="calendar-grid" id="calendarGrid">
                                <!-- 日曆將由 JavaScript 生成 -->
                            </div>
                        </div>
                    </div>

                    <!-- 時段詳情 -->
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header">
                                <h6><i class="fas fa-list"></i> 選定日期時段</h6>
                                <small id="selectedDate" class="text-muted">請選擇日期</small>
                            </div>
                            <div class="card-body" id="slotsContainer">
                                <p class="text-muted text-center">請點擊日曆選擇日期</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 時段編輯模態框 -->
    <div class="modal fade" id="slotModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="modalTitle">新增時段</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="slotForm">
                        <input type="hidden" id="slotId">
                        <div class="mb-3">
                            <label for="slotDate" class="form-label">日期 *</label>
                            <input type="date" class="form-control" id="slotDate" required>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="startTime" class="form-label">開始時間 *</label>
                                    <input type="time" class="form-control" id="startTime" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="endTime" class="form-label">結束時間 *</label>
                                    <input type="time" class="form-control" id="endTime" required>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="maxAppointments" class="form-label">最大預約數</label>
                                    <input type="number" class="form-control" id="maxAppointments" value="1" min="1" max="10">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="isAvailable" class="form-label">狀態</label>
                                    <select class="form-select" id="isAvailable">
                                        <option value="true">可用</option>
                                        <option value="false">停用</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="notes" class="form-label">備註</label>
                            <textarea class="form-control" id="notes" rows="3" placeholder="時段相關備註..."></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="saveSlot()">儲存</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 批量建立模態框 -->
    <div class="modal fade" id="batchModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">批量建立時段</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="batchForm">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="batchStartDate" class="form-label">開始日期 *</label>
                                    <input type="date" class="form-control" id="batchStartDate" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="batchEndDate" class="form-label">結束日期 *</label>
                                    <input type="date" class="form-control" id="batchEndDate" required>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">選擇星期 *</label>
                            <div class="row">
                                <div class="col-md-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="day1" value="1">
                                        <label class="form-check-label" for="day1">星期一</label>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="day2" value="2">
                                        <label class="form-check-label" for="day2">星期二</label>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="day3" value="3">
                                        <label class="form-check-label" for="day3">星期三</label>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="day4" value="4">
                                        <label class="form-check-label" for="day4">星期四</label>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="day5" value="5">
                                        <label class="form-check-label" for="day5">星期五</label>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="day6" value="6">
                                        <label class="form-check-label" for="day6">星期六</label>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="day0" value="0">
                                        <label class="form-check-label" for="day0">星期日</label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div id="timeSlotTemplates">
                            <div class="mb-3">
                                <label class="form-label">時段模板</label>
                                <div class="time-slot-template border p-3 rounded">
                                    <div class="row">
                                        <div class="col-md-4">
                                            <input type="time" class="form-control" placeholder="開始時間" value="09:00">
                                        </div>
                                        <div class="col-md-4">
                                            <input type="time" class="form-control" placeholder="結束時間" value="10:00">
                                        </div>
                                        <div class="col-md-3">
                                            <input type="number" class="form-control" placeholder="最大預約數" value="1" min="1">
                                        </div>
                                        <div class="col-md-1">
                                            <button type="button" class="btn btn-outline-danger btn-sm" onclick="removeTimeTemplate(this)">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <button type="button" class="btn btn-outline-primary" onclick="addTimeTemplate()">
                            <i class="fas fa-plus"></i> 新增時段模板
                        </button>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="saveBatchSlots()">建立時段</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let timeSlots = [];
        let currentDate = new Date();
        let selectedDate = null;
        let slotModal, batchModal;

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            slotModal = new bootstrap.Modal(document.getElementById('slotModal'));
            batchModal = new bootstrap.Modal(document.getElementById('batchModal'));
            
            initializeCalendar();
            loadTimeSlots();
            loadStatistics();

            // 設置預設日期
            const today = new Date();
            document.getElementById('slotDate').value = today.toISOString().split('T')[0];
            document.getElementById('batchStartDate').value = today.toISOString().split('T')[0];
            
            const nextWeek = new Date(today);
            nextWeek.setDate(today.getDate() + 7);
            document.getElementById('batchEndDate').value = nextWeek.toISOString().split('T')[0];
        });

        // 初始化日曆
        function initializeCalendar() {
            document.getElementById('prevMonth').addEventListener('click', () => {
                currentDate.setMonth(currentDate.getMonth() - 1);
                renderCalendar();
            });

            document.getElementById('nextMonth').addEventListener('click', () => {
                currentDate.setMonth(currentDate.getMonth() + 1);
                renderCalendar();
            });

            renderCalendar();
        }

        // 渲染日曆
        function renderCalendar() {
            const year = currentDate.getFullYear();
            const month = currentDate.getMonth();
            
            document.getElementById('currentMonth').textContent = 
                `${year}年 ${month + 1}月`;

            const firstDay = new Date(year, month, 1);
            const lastDay = new Date(year, month + 1, 0);
            const startDate = new Date(firstDay);
            startDate.setDate(startDate.getDate() - firstDay.getDay());

            const calendarGrid = document.getElementById('calendarGrid');
            calendarGrid.innerHTML = '';

            // 星期標題
            const weekdays = ['日', '一', '二', '三', '四', '五', '六'];
            weekdays.forEach(day => {
                const dayHeader = document.createElement('div');
                dayHeader.className = 'calendar-day';
                dayHeader.style.background = '#f8f9fa';
                dayHeader.style.fontWeight = 'bold';
                dayHeader.style.cursor = 'default';
                dayHeader.textContent = day;
                calendarGrid.appendChild(dayHeader);
            });

            // 日期
            for (let i = 0; i < 42; i++) {
                const date = new Date(startDate);
                date.setDate(startDate.getDate() + i);
                
                const dayElement = document.createElement('div');
                dayElement.className = 'calendar-day';
                
                const dayNumber = document.createElement('div');
                dayNumber.textContent = date.getDate();
                dayElement.appendChild(dayNumber);
                
                if (date.getMonth() !== month) {
                    dayElement.classList.add('disabled');
                } else {
                    dayElement.addEventListener('click', () => selectDate(date));
                    
                    // 檢查是否有時段
                    const dateStr = date.toISOString().split('T')[0];
                    const daySlots = timeSlots.filter(slot => slot.date === dateStr);
                    
                    if (daySlots.length > 0) {
                        dayElement.classList.add('has-slots');
                        const indicator = document.createElement('div');
                        indicator.className = 'slot-indicator';
                        indicator.textContent = `${daySlots.length} 時段`;
                        dayElement.appendChild(indicator);
                    }
                }
                
                calendarGrid.appendChild(dayElement);
            }
        }

        // 選擇日期
        function selectDate(date) {
            selectedDate = date;
            document.querySelectorAll('.calendar-day').forEach(day => {
                day.classList.remove('selected');
            });
            event.target.closest('.calendar-day').classList.add('selected');
            
            loadDaySlots(date);
        }

        // 載入時段資料
        function loadTimeSlots() {
            const year = currentDate.getFullYear();
            const month = currentDate.getMonth() + 1;
            
            fetch(`/api/admin/v2/time-slots?year=${year}&month=${month}`)
            .then(response => response.json())
            .then(data => {
                if (data.msg === '取得時段列表成功') {
                    timeSlots = data.data;
                    renderCalendar();
                    updateStatistics();
                }
            })
            .catch(error => {
                console.error('載入時段失敗:', error);
            });
        }

        // 載入指定日期的時段
        function loadDaySlots(date) {
            const dateStr = date.toISOString().split('T')[0];
            const daySlots = timeSlots.filter(slot => slot.date === dateStr);
            
            document.getElementById('selectedDate').textContent = 
                date.toLocaleDateString('zh-TW', { 
                    year: 'numeric', 
                    month: 'long', 
                    day: 'numeric',
                    weekday: 'long'
                });

            const container = document.getElementById('slotsContainer');
            
            if (daySlots.length === 0) {
                container.innerHTML = `
                    <p class="text-muted text-center">此日期暫無時段</p>
                    <button class="btn btn-outline-primary w-100" onclick="createSlotForDate('${dateStr}')">
                        <i class="fas fa-plus"></i> 新增時段
                    </button>
                `;
                return;
            }

            container.innerHTML = daySlots.map(slot => `
                <div class="slot-card">
                    <div class="d-flex justify-content-between align-items-start mb-2">
                        <div>
                            <strong>${slot.start_time} - ${slot.end_time}</strong>
                            <div class="mt-1">
                                <span class="slot-status status-${slot.is_available ? 'available' : 'disabled'}">
                                    ${slot.is_available ? '可用' : '停用'}
                                </span>
                            </div>
                        </div>
                        <div class="dropdown">
                            <button class="btn btn-outline-secondary btn-sm dropdown-toggle" data-bs-toggle="dropdown">
                                <i class="fas fa-cog"></i>
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="#" onclick="editSlot(${slot.id})">
                                    <i class="fas fa-edit"></i> 編輯
                                </a></li>
                                <li><a class="dropdown-item text-danger" href="#" onclick="deleteSlot(${slot.id})">
                                    <i class="fas fa-trash"></i> 刪除
                                </a></li>
                            </ul>
                        </div>
                    </div>
                    
                    <div class="small text-muted">
                        最大預約數: ${slot.max_appointments} | 
                        已預約: ${slot.current_appointments || 0}
                    </div>
                    
                    ${slot.notes ? `<div class="small text-muted mt-1">備註: ${slot.notes}</div>` : ''}
                </div>
            `).join('');
        }

        // 載入統計資料
        function loadStatistics() {
            fetch('/api/admin/v2/time-slots/statistics')
            .then(response => response.json())
            .then(data => {
                if (data.msg === '取得統計資料成功') {
                    const stats = data.data;
                    document.getElementById('totalSlots').textContent = stats.total_slots || 0;
                    document.getElementById('availableSlots').textContent = stats.available_slots || 0;
                    document.getElementById('todaySlots').textContent = stats.today_slots || 0;
                    document.getElementById('bookedSlots').textContent = stats.booked_slots || 0;
                }
            });
        }

        // 開啟新增模態框
        function openCreateModal() {
            document.getElementById('modalTitle').textContent = '新增時段';
            document.getElementById('slotForm').reset();
            document.getElementById('slotId').value = '';
            document.getElementById('isAvailable').value = 'true';
            document.getElementById('maxAppointments').value = '1';
            
            if (selectedDate) {
                document.getElementById('slotDate').value = selectedDate.toISOString().split('T')[0];
            }
        }

        // 為指定日期建立時段
        function createSlotForDate(dateStr) {
            document.getElementById('modalTitle').textContent = '新增時段';
            document.getElementById('slotForm').reset();
            document.getElementById('slotId').value = '';
            document.getElementById('slotDate').value = dateStr;
            document.getElementById('isAvailable').value = 'true';
            document.getElementById('maxAppointments').value = '1';
            slotModal.show();
        }

        // 編輯時段
        function editSlot(slotId) {
            const slot = timeSlots.find(s => s.id === slotId);
            if (!slot) return;

            document.getElementById('modalTitle').textContent = '編輯時段';
            document.getElementById('slotId').value = slot.id;
            document.getElementById('slotDate').value = slot.date;
            document.getElementById('startTime').value = slot.start_time;
            document.getElementById('endTime').value = slot.end_time;
            document.getElementById('maxAppointments').value = slot.max_appointments;
            document.getElementById('isAvailable').value = slot.is_available.toString();
            document.getElementById('notes').value = slot.notes || '';

            slotModal.show();
        }

        // 儲存時段
        function saveSlot() {
            const slotId = document.getElementById('slotId').value;
            const isEdit = !!slotId;

            const slotData = {
                date: document.getElementById('slotDate').value,
                start_time: document.getElementById('startTime').value,
                end_time: document.getElementById('endTime').value,
                max_appointments: parseInt(document.getElementById('maxAppointments').value),
                is_available: document.getElementById('isAvailable').value === 'true',
                notes: document.getElementById('notes').value
            };

            const url = isEdit ? `/api/admin/v2/time-slots/${slotId}` : '/api/admin/v2/time-slots';
            const method = isEdit ? 'PUT' : 'POST';

            fetch(url, {
                method: method,
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(slotData)
            })
            .then(response => response.json())
            .then(data => {
                if (data.msg.includes('成功')) {
                    slotModal.hide();
                    loadTimeSlots();
                    if (selectedDate) {
                        loadDaySlots(selectedDate);
                    }
                    alert(isEdit ? '時段更新成功！' : '時段建立成功！');
                } else {
                    alert('操作失敗: ' + data.error);
                }
            })
            .catch(error => {
                alert('操作失敗: ' + error.message);
            });
        }

        // 刪除時段
        function deleteSlot(slotId) {
            if (!confirm('確定要刪除此時段嗎？')) return;

            fetch(`/api/admin/v2/time-slots/${slotId}`, {
                method: 'DELETE'
            })
            .then(response => response.json())
            .then(data => {
                if (data.msg === '刪除時段成功') {
                    loadTimeSlots();
                    if (selectedDate) {
                        loadDaySlots(selectedDate);
                    }
                    alert('時段刪除成功！');
                } else {
                    alert('刪除失敗: ' + data.error);
                }
            })
            .catch(error => {
                alert('刪除失敗: ' + error.message);
            });
        }

        // 批量建立時段
        function batchCreateSlots() {
            batchModal.show();
        }

        // 新增時段模板
        function addTimeTemplate() {
            const container = document.getElementById('timeSlotTemplates');
            const template = document.createElement('div');
            template.className = 'mb-3';
            template.innerHTML = `
                <div class="time-slot-template border p-3 rounded">
                    <div class="row">
                        <div class="col-md-4">
                            <input type="time" class="form-control" placeholder="開始時間">
                        </div>
                        <div class="col-md-4">
                            <input type="time" class="form-control" placeholder="結束時間">
                        </div>
                        <div class="col-md-3">
                            <input type="number" class="form-control" placeholder="最大預約數" value="1" min="1">
                        </div>
                        <div class="col-md-1">
                            <button type="button" class="btn btn-outline-danger btn-sm" onclick="removeTimeTemplate(this)">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                </div>
            `;
            container.appendChild(template);
        }

        // 移除時段模板
        function removeTimeTemplate(button) {
            button.closest('.mb-3').remove();
        }

        // 儲存批量時段
        function saveBatchSlots() {
            const startDate = document.getElementById('batchStartDate').value;
            const endDate = document.getElementById('batchEndDate').value;
            
            // 取得選中的星期
            const selectedDays = [];
            for (let i = 0; i <= 6; i++) {
                if (document.getElementById(`day${i}`).checked) {
                    selectedDays.push(i);
                }
            }

            if (selectedDays.length === 0) {
                alert('請至少選擇一個星期');
                return;
            }

            // 取得時段模板
            const templates = [];
            document.querySelectorAll('.time-slot-template').forEach(template => {
                const inputs = template.querySelectorAll('input');
                if (inputs[0].value && inputs[1].value) {
                    templates.push({
                        start_time: inputs[0].value,
                        end_time: inputs[1].value,
                        max_appointments: parseInt(inputs[2].value) || 1
                    });
                }
            });

            if (templates.length === 0) {
                alert('請至少設定一個時段模板');
                return;
            }

            const batchData = {
                start_date: startDate,
                end_date: endDate,
                days_of_week: selectedDays,
                time_templates: templates
            };

            fetch('/api/admin/v2/time-slots/batch', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(batchData)
            })
            .then(response => response.json())
            .then(data => {
                if (data.msg === '批量建立時段成功') {
                    batchModal.hide();
                    loadTimeSlots();
                    alert(`成功建立 ${data.data.created_count} 個時段！`);
                } else {
                    alert('建立失敗: ' + data.error);
                }
            })
            .catch(error => {
                alert('建立失敗: ' + error.message);
            });
        }

        // 更新統計
        function updateStatistics() {
            const totalSlots = timeSlots.length;
            const availableSlots = timeSlots.filter(s => s.is_available).length;
            
            const today = new Date().toISOString().split('T')[0];
            const todaySlots = timeSlots.filter(s => s.date === today).length;

            document.getElementById('totalSlots').textContent = totalSlots;
            document.getElementById('availableSlots').textContent = availableSlots;
            document.getElementById('todaySlots').textContent = todaySlots;
        }

        // 快速操作函數
        function createWeeklySlots() {
            // 實作建立本週時段的邏輯
            alert('功能開發中...');
        }

        function createMonthlySlots() {
            // 實作建立本月時段的邏輯
            alert('功能開發中...');
        }

        function copyPreviousWeek() {
            // 實作複製上週時段的邏輯
            alert('功能開發中...');
        }

        function clearFutureSlots() {
            if (!confirm('確定要清除所有未來的時段嗎？此操作無法復原。')) return;
            // 實作清除未來時段的邏輯
            alert('功能開發中...');
        }
    </script>
</body>
</html>
{{end}}
