{{define "consultation_v2/assignment_list"}}
<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{.Module.Name}} - 作業列表 - G.R.A.C.E 優雅學院</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .module-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px 0;
            margin-bottom: 30px;
        }
        .assignment-card {
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            padding: 25px;
            margin-bottom: 25px;
            background: #fff;
            transition: all 0.2s ease;
            position: relative;
        }
        .assignment-card:hover {
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            transform: translateY(-2px);
        }
        .assignment-number {
            position: absolute;
            top: -10px;
            left: 20px;
            background: #007bff;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 14px;
        }
        .status-badge {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
        }
        .status-not-started { background: #e2e3e5; color: #6c757d; }
        .status-pending { background: #fff3cd; color: #856404; }
        .status-approved { background: #d4edda; color: #155724; }
        .status-rejected { background: #f8d7da; color: #721c24; }
        .assignment-completed {
            border-left: 4px solid #28a745;
        }
        .assignment-pending {
            border-left: 4px solid #ffc107;
        }
        .assignment-rejected {
            border-left: 4px solid #dc3545;
        }
        .assignment-not-started {
            border-left: 4px solid #6c757d;
        }
        .progress-section {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .submission-history {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin-top: 15px;
        }
        .history-item {
            padding: 10px 0;
            border-bottom: 1px solid #e0e0e0;
        }
        .history-item:last-child {
            border-bottom: none;
        }
        .requirements-section {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }
        .action-buttons {
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <!-- 模組標題區域 -->
    <div class="module-header">
        <div class="container">
            <div class="row">
                <div class="col-md-10 mx-auto">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h2>{{.Module.Name}}</h2>
                            <p class="mb-0">作業列表與提交狀態</p>
                        </div>
                        <div>
                            <a href="/consultations/v2/modules/{{.Module.ID}}" class="btn btn-light">
                                <i class="fas fa-arrow-left"></i> 返回模組詳情
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <div class="row">
            <div class="col-md-8">
                <!-- 作業列表 -->
                {{if .Assignments}}
                    {{range $index, $assignment := .Assignments}}
                    {{$status := index $.SubmissionStatus $assignment.ID}}
                    <div class="assignment-card assignment-{{$status}}">
                        <div class="assignment-number">{{add $index 1}}</div>
                        
                        <div class="d-flex justify-content-between align-items-start mb-3">
                            <div class="flex-grow-1">
                                <h4>{{$assignment.Title}}</h4>
                                <p class="text-muted mb-3">{{$assignment.Description}}</p>
                            </div>
                            <div class="text-end">
                                <span class="status-badge status-{{$status}}">
                                    {{getStatusText $status}}
                                </span>
                            </div>
                        </div>

                        {{if $assignment.Requirements}}
                        <div class="requirements-section">
                            <h6><i class="fas fa-clipboard-list"></i> 提交要求</h6>
                            <p class="mb-0">{{$assignment.Requirements}}</p>
                        </div>
                        {{end}}

                        <!-- 提交歷史 -->
                        {{$submissions := getSubmissionHistory $assignment.ID}}
                        {{if $submissions}}
                        <div class="submission-history">
                            <h6><i class="fas fa-history"></i> 提交歷史</h6>
                            {{range $submissions}}
                            <div class="history-item">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div>
                                        <div class="small text-muted">{{formatDateTime .SubmittedAt}}</div>
                                        <div class="fw-bold">狀態：{{getStatusText .Status}}</div>
                                        {{if .ReviewNote}}
                                        <div class="small text-muted mt-1">審核意見：{{.ReviewNote}}</div>
                                        {{end}}
                                    </div>
                                    <div>
                                        <a href="/consultations/v2/assignments/{{$assignment.ID}}/submit" class="btn btn-outline-info btn-sm">
                                            <i class="fas fa-eye"></i> 查看
                                        </a>
                                    </div>
                                </div>
                            </div>
                            {{end}}
                        </div>
                        {{end}}

                        <div class="action-buttons">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    {{if eq $status "not_started"}}
                                        <span class="text-muted small">
                                            <i class="fas fa-info-circle"></i> 尚未開始提交
                                        </span>
                                    {{else if eq $status "pending"}}
                                        <span class="text-warning small">
                                            <i class="fas fa-clock"></i> 等待審核中
                                        </span>
                                    {{else if eq $status "approved"}}
                                        <span class="text-success small">
                                            <i class="fas fa-check-circle"></i> 作業已通過審核
                                        </span>
                                    {{else if eq $status "rejected"}}
                                        <span class="text-danger small">
                                            <i class="fas fa-exclamation-triangle"></i> 需要重新提交
                                        </span>
                                    {{end}}
                                </div>
                                
                                <div>
                                    {{if eq $status "not_started"}}
                                        <a href="/consultations/v2/assignments/{{$assignment.ID}}/submit" class="btn btn-primary">
                                            <i class="fas fa-upload"></i> 開始提交
                                        </a>
                                    {{else if eq $status "rejected"}}
                                        <a href="/consultations/v2/assignments/{{$assignment.ID}}/submit" class="btn btn-warning">
                                            <i class="fas fa-edit"></i> 重新提交
                                        </a>
                                    {{else}}
                                        <a href="/consultations/v2/assignments/{{$assignment.ID}}/submit" class="btn btn-outline-secondary">
                                            <i class="fas fa-eye"></i> 查看提交
                                        </a>
                                    {{end}}
                                </div>
                            </div>
                        </div>
                    </div>
                    {{end}}
                {{else}}
                    <div class="text-center py-5">
                        <i class="fas fa-clipboard fa-4x text-muted mb-4"></i>
                        <h4>此模組暫無作業要求</h4>
                        <p class="text-muted">您可以直接進行預約諮詢</p>
                        <a href="/consultations/v2/book?module_id={{.Module.ID}}" class="btn btn-primary btn-lg">
                            <i class="fas fa-calendar-plus"></i> 立即預約
                        </a>
                    </div>
                {{end}}
            </div>

            <div class="col-md-4">
                <!-- 整體進度 -->
                <div class="progress-section">
                    <h5><i class="fas fa-chart-pie"></i> 完成進度</h5>
                    
                    <div class="text-center mb-3">
                        <div class="progress" style="height: 20px;">
                            <div class="progress-bar progress-bar-striped" role="progressbar" 
                                 style="width: {{calculateProgress .Assignments .SubmissionStatus}}%" 
                                 aria-valuenow="{{calculateProgress .Assignments .SubmissionStatus}}" 
                                 aria-valuemin="0" aria-valuemax="100">
                                {{calculateProgress .Assignments .SubmissionStatus}}%
                            </div>
                        </div>
                        <small class="text-muted">整體完成度</small>
                    </div>

                    <div class="row text-center">
                        <div class="col-6">
                            <div class="fw-bold text-success">{{countByStatus .SubmissionStatus "approved"}}</div>
                            <small class="text-muted">已完成</small>
                        </div>
                        <div class="col-6">
                            <div class="fw-bold text-primary">{{len .Assignments}}</div>
                            <small class="text-muted">總作業數</small>
                        </div>
                    </div>
                </div>

                <!-- 狀態統計 -->
                <div class="progress-section">
                    <h5><i class="fas fa-list-check"></i> 狀態統計</h5>
                    
                    <div class="mb-2">
                        <div class="d-flex justify-content-between align-items-center">
                            <span class="small">
                                <span class="status-badge status-approved">已通過</span>
                            </span>
                            <span class="fw-bold">{{countByStatus .SubmissionStatus "approved"}}</span>
                        </div>
                    </div>
                    
                    <div class="mb-2">
                        <div class="d-flex justify-content-between align-items-center">
                            <span class="small">
                                <span class="status-badge status-pending">審核中</span>
                            </span>
                            <span class="fw-bold">{{countByStatus .SubmissionStatus "pending"}}</span>
                        </div>
                    </div>
                    
                    <div class="mb-2">
                        <div class="d-flex justify-content-between align-items-center">
                            <span class="small">
                                <span class="status-badge status-rejected">需修改</span>
                            </span>
                            <span class="fw-bold">{{countByStatus .SubmissionStatus "rejected"}}</span>
                        </div>
                    </div>
                    
                    <div class="mb-2">
                        <div class="d-flex justify-content-between align-items-center">
                            <span class="small">
                                <span class="status-badge status-not-started">未開始</span>
                            </span>
                            <span class="fw-bold">{{countByStatus .SubmissionStatus "not_started"}}</span>
                        </div>
                    </div>
                </div>

                <!-- 下一步行動 -->
                <div class="progress-section">
                    <h5><i class="fas fa-route"></i> 下一步</h5>
                    
                    {{$nextAction := getNextAction .Assignments .SubmissionStatus}}
                    {{if eq $nextAction.Type "submit"}}
                        <div class="alert alert-info">
                            <i class="fas fa-upload"></i>
                            <strong>需要提交作業</strong>
                            <p class="mb-2 small">您還有 {{$nextAction.Count}} 個作業尚未提交</p>
                            <a href="/consultations/v2/assignments/{{$nextAction.AssignmentID}}/submit" class="btn btn-primary btn-sm">
                                提交下一個作業
                            </a>
                        </div>
                    {{else if eq $nextAction.Type "resubmit"}}
                        <div class="alert alert-warning">
                            <i class="fas fa-edit"></i>
                            <strong>需要重新提交</strong>
                            <p class="mb-2 small">您有 {{$nextAction.Count}} 個作業需要修改</p>
                            <a href="/consultations/v2/assignments/{{$nextAction.AssignmentID}}/submit" class="btn btn-warning btn-sm">
                                修改作業
                            </a>
                        </div>
                    {{else if eq $nextAction.Type "wait"}}
                        <div class="alert alert-secondary">
                            <i class="fas fa-clock"></i>
                            <strong>等待審核</strong>
                            <p class="mb-0 small">您的作業正在審核中，請耐心等待</p>
                        </div>
                    {{else if eq $nextAction.Type "book"}}
                        <div class="alert alert-success">
                            <i class="fas fa-calendar-plus"></i>
                            <strong>可以預約了！</strong>
                            <p class="mb-2 small">所有作業已完成，現在可以預約諮詢</p>
                            <a href="/consultations/v2/book?module_id={{.Module.ID}}" class="btn btn-success btn-sm">
                                立即預約
                            </a>
                        </div>
                    {{end}}
                </div>

                <!-- 相關連結 -->
                <div class="progress-section">
                    <h5><i class="fas fa-link"></i> 相關連結</h5>
                    
                    <div class="d-grid gap-2">
                        <a href="/consultations/v2/modules/{{.Module.ID}}" class="btn btn-outline-primary">
                            <i class="fas fa-info-circle"></i> 模組詳情
                        </a>
                        <a href="/consultations/v2/appointments" class="btn btn-outline-info">
                            <i class="fas fa-calendar-check"></i> 我的預約
                        </a>
                        <a href="/consultations/v2" class="btn btn-outline-secondary">
                            <i class="fas fa-home"></i> 模組列表
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 輔助函數
        function getStatusText(status) {
            const statusMap = {
                'not_started': '未開始',
                'pending': '審核中',
                'approved': '已通過',
                'rejected': '需修改'
            };
            return statusMap[status] || status;
        }

        function formatDateTime(dateTimeStr) {
            const date = new Date(dateTimeStr);
            return date.toLocaleString('zh-TW', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit'
            });
        }

        function calculateProgress(assignments, submissionStatus) {
            if (assignments.length === 0) return 100;
            
            let completedCount = 0;
            assignments.forEach(assignment => {
                const status = submissionStatus[assignment.id];
                if (status === 'approved') {
                    completedCount++;
                }
            });
            
            return Math.round((completedCount / assignments.length) * 100);
        }

        function countByStatus(submissionStatus, targetStatus) {
            let count = 0;
            Object.values(submissionStatus).forEach(status => {
                if (status === targetStatus) {
                    count++;
                }
            });
            return count;
        }

        function getNextAction(assignments, submissionStatus) {
            // 檢查是否有需要重新提交的作業
            for (let assignment of assignments) {
                const status = submissionStatus[assignment.id];
                if (status === 'rejected') {
                    return {
                        type: 'resubmit',
                        assignmentId: assignment.id,
                        count: countByStatus(submissionStatus, 'rejected')
                    };
                }
            }
            
            // 檢查是否有未開始的作業
            for (let assignment of assignments) {
                const status = submissionStatus[assignment.id] || 'not_started';
                if (status === 'not_started') {
                    return {
                        type: 'submit',
                        assignmentId: assignment.id,
                        count: countByStatus(submissionStatus, 'not_started')
                    };
                }
            }
            
            // 檢查是否有待審核的作業
            if (countByStatus(submissionStatus, 'pending') > 0) {
                return {
                    type: 'wait',
                    count: countByStatus(submissionStatus, 'pending')
                };
            }
            
            // 所有作業都完成了
            return {
                type: 'book'
            };
        }
    </script>
</body>
</html>
{{end}}
