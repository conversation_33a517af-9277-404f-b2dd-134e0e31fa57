{{define "admin/consultation_v2/appointments"}}
<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>預約管理 - G.R.A.C.E 優雅學院後台</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .appointment-card {
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            background: #fff;
            transition: all 0.2s ease;
        }
        .appointment-card:hover {
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .status-badge {
            padding: 5px 10px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
        }
        .status-pending { background: #fff3cd; color: #856404; }
        .status-confirmed { background: #d1ecf1; color: #0c5460; }
        .status-completed { background: #d4edda; color: #155724; }
        .status-cancelled { background: #f8d7da; color: #721c24; }
        .priority-high { border-left: 4px solid #dc3545; }
        .priority-normal { border-left: 4px solid #28a745; }
        .filter-section {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .member-info {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 10px;
        }
        .zoom-info {
            background: #e3f2fd;
            padding: 10px;
            border-radius: 5px;
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <div class="container-fluid mt-4">
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2><i class="fas fa-calendar-check"></i> 預約管理</h2>
                    <div>
                        <button class="btn btn-primary" onclick="exportAppointments()">
                            <i class="fas fa-download"></i> 匯出資料
                        </button>
                        <a href="/admin/consultations/v2" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left"></i> 返回總覽
                        </a>
                    </div>
                </div>

                <!-- 統計卡片 -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="stats-card">
                            <h5><i class="fas fa-clock"></i> 待確認</h5>
                            <h2 id="pendingCount">0</h2>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stats-card">
                            <h5><i class="fas fa-check-circle"></i> 已確認</h5>
                            <h2 id="confirmedCount">0</h2>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stats-card">
                            <h5><i class="fas fa-calendar-day"></i> 今日預約</h5>
                            <h2 id="todayCount">0</h2>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stats-card">
                            <h5><i class="fas fa-chart-line"></i> 本月總數</h5>
                            <h2 id="monthlyCount">0</h2>
                        </div>
                    </div>
                </div>

                <!-- 篩選區域 -->
                <div class="filter-section">
                    <div class="row">
                        <div class="col-md-2">
                            <label class="form-label">狀態篩選</label>
                            <select class="form-select" id="statusFilter" onchange="filterAppointments()">
                                <option value="">全部狀態</option>
                                <option value="pending">待確認</option>
                                <option value="confirmed">已確認</option>
                                <option value="completed">已完成</option>
                                <option value="cancelled">已取消</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">模組篩選</label>
                            <select class="form-select" id="moduleFilter" onchange="filterAppointments()">
                                <option value="">全部模組</option>
                                <!-- 模組選項將由 JavaScript 動態載入 -->
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">日期範圍</label>
                            <select class="form-select" id="dateFilter" onchange="filterAppointments()">
                                <option value="">全部日期</option>
                                <option value="today">今天</option>
                                <option value="tomorrow">明天</option>
                                <option value="week">本週</option>
                                <option value="month">本月</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">搜尋</label>
                            <input type="text" class="form-control" id="searchInput" placeholder="搜尋會員姓名或預約主題..." onkeyup="filterAppointments()">
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">排序</label>
                            <select class="form-select" id="sortOrder" onchange="filterAppointments()">
                                <option value="datetime_desc">預約時間 (新到舊)</option>
                                <option value="datetime_asc">預約時間 (舊到新)</option>
                                <option value="created_desc">建立時間 (新到舊)</option>
                                <option value="status">狀態</option>
                            </select>
                        </div>
                    </div>
                </div>

                <!-- 預約列表 -->
                <div id="appointmentsList">
                    <!-- 預約卡片將由 JavaScript 動態載入 -->
                </div>

                <!-- 分頁 -->
                <nav aria-label="預約分頁" id="pagination" style="display: none;">
                    <ul class="pagination justify-content-center">
                        <!-- 分頁將由 JavaScript 生成 -->
                    </ul>
                </nav>
            </div>
        </div>
    </div>

    <!-- 預約詳情模態框 -->
    <div class="modal fade" id="appointmentModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">預約詳情</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="appointmentDetails">
                    <!-- 詳情內容將由 JavaScript 動態載入 -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">關閉</button>
                    <div id="appointmentActions">
                        <!-- 操作按鈕將由 JavaScript 動態載入 -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 確認操作模態框 -->
    <div class="modal fade" id="confirmModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="confirmTitle">確認操作</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p id="confirmMessage"></p>
                    <div id="confirmForm" style="display: none;">
                        <div class="mb-3">
                            <label for="actionNote" class="form-label">備註</label>
                            <textarea class="form-control" id="actionNote" rows="3" placeholder="請輸入操作備註..."></textarea>
                        </div>
                        <div id="zoomSection" style="display: none;">
                            <div class="mb-3">
                                <label for="zoomUrl" class="form-label">Zoom 會議連結</label>
                                <input type="url" class="form-control" id="zoomUrl" placeholder="https://zoom.us/j/...">
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="confirmActionBtn">確認</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let appointments = [];
        let filteredAppointments = [];
        let modules = [];
        let currentPage = 1;
        let totalPages = 1;
        let appointmentModal, confirmModal;
        let currentAction = null;

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            appointmentModal = new bootstrap.Modal(document.getElementById('appointmentModal'));
            confirmModal = new bootstrap.Modal(document.getElementById('confirmModal'));
            
            loadAppointments();
            loadModules();
            loadStatistics();
        });

        // 載入預約列表
        function loadAppointments() {
            const params = new URLSearchParams({
                page: currentPage,
                limit: 20
            });

            fetch(`/api/admin/v2/appointments?${params}`)
            .then(response => response.json())
            .then(data => {
                if (data.msg === '取得預約列表成功') {
                    appointments = data.data.appointments;
                    filteredAppointments = [...appointments];
                    totalPages = Math.ceil(data.data.total / 20);
                    renderAppointments();
                    updateStatistics();
                } else {
                    console.error('載入預約失敗:', data.error);
                }
            })
            .catch(error => {
                console.error('載入預約失敗:', error);
            });
        }

        // 載入模組列表
        function loadModules() {
            fetch('/api/admin/v2/modules')
            .then(response => response.json())
            .then(data => {
                if (data.msg === '取得模組列表成功') {
                    modules = data.data;
                    renderModuleFilter();
                }
            });
        }

        // 渲染模組篩選選項
        function renderModuleFilter() {
            const moduleFilter = document.getElementById('moduleFilter');
            moduleFilter.innerHTML = '<option value="">全部模組</option>';
            
            modules.forEach(module => {
                const option = document.createElement('option');
                option.value = module.id;
                option.textContent = module.name;
                moduleFilter.appendChild(option);
            });
        }

        // 載入統計資料
        function loadStatistics() {
            fetch('/api/admin/v2/appointments/statistics')
            .then(response => response.json())
            .then(data => {
                if (data.msg === '取得統計資料成功') {
                    const stats = data.data;
                    document.getElementById('pendingCount').textContent = stats.pending_count || 0;
                    document.getElementById('confirmedCount').textContent = stats.confirmed_count || 0;
                    document.getElementById('todayCount').textContent = stats.today_count || 0;
                    document.getElementById('monthlyCount').textContent = stats.monthly_count || 0;
                }
            });
        }

        // 渲染預約列表
        function renderAppointments() {
            const container = document.getElementById('appointmentsList');
            
            if (filteredAppointments.length === 0) {
                container.innerHTML = `
                    <div class="text-center py-5">
                        <i class="fas fa-calendar-times fa-3x text-muted mb-3"></i>
                        <h5>暫無預約記錄</h5>
                        <p class="text-muted">目前沒有符合篩選條件的預約記錄</p>
                    </div>
                `;
                return;
            }

            container.innerHTML = filteredAppointments.map(appointment => {
                const isUrgent = isAppointmentUrgent(appointment);
                return `
                    <div class="appointment-card ${isUrgent ? 'priority-high' : 'priority-normal'}">
                        <div class="d-flex justify-content-between align-items-start mb-3">
                            <div>
                                <h5>${appointment.title}</h5>
                                <div class="member-info">
                                    <strong>${appointment.member.name}</strong> (${appointment.member.uid})
                                    <br><small>會員等級: ${getMemberLevelText(appointment.member.level)}</small>
                                </div>
                            </div>
                            <div class="text-end">
                                <span class="status-badge status-${appointment.status}">
                                    ${getStatusText(appointment.status)}
                                </span>
                                ${isUrgent ? '<div class="text-danger small mt-1"><i class="fas fa-exclamation-triangle"></i> 緊急</div>' : ''}
                            </div>
                        </div>
                        
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <p><i class="fas fa-puzzle-piece"></i> <strong>模組:</strong> ${appointment.module.name}</p>
                                <p><i class="fas fa-calendar"></i> <strong>預約時間:</strong> ${formatDateTime(appointment.appointment_datetime)}</p>
                                <p><i class="fas fa-clock"></i> <strong>時長:</strong> ${appointment.duration_minutes} 分鐘</p>
                            </div>
                            <div class="col-md-6">
                                <p><i class="fas fa-user-clock"></i> <strong>建立時間:</strong> ${formatDateTime(appointment.created_at)}</p>
                                ${appointment.confirmed_at ? `<p><i class="fas fa-check"></i> <strong>確認時間:</strong> ${formatDateTime(appointment.confirmed_at)}</p>` : ''}
                                ${appointment.zoom_join_url ? `
                                    <div class="zoom-info">
                                        <i class="fas fa-video"></i> <strong>Zoom 會議:</strong>
                                        <a href="${appointment.zoom_join_url}" target="_blank" class="text-decoration-none">加入會議</a>
                                    </div>
                                ` : ''}
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <strong>預約說明:</strong>
                            <p class="mb-2">${appointment.description}</p>
                            ${appointment.notes ? `<p class="text-muted small">備註: ${appointment.notes}</p>` : ''}
                        </div>
                        
                        <div class="d-flex justify-content-between align-items-center">
                            <button class="btn btn-outline-info btn-sm" onclick="viewAppointmentDetails(${appointment.id})">
                                <i class="fas fa-eye"></i> 查看詳情
                            </button>
                            <div>
                                ${getActionButtons(appointment)}
                            </div>
                        </div>
                    </div>
                `;
            }).join('');
        }

        // 檢查預約是否緊急
        function isAppointmentUrgent(appointment) {
            if (appointment.status !== 'pending') return false;
            
            const appointmentTime = new Date(appointment.appointment_datetime);
            const now = new Date();
            const hoursDiff = (appointmentTime - now) / (1000 * 60 * 60);
            
            return hoursDiff <= 24 && hoursDiff > 0; // 24小時內的待確認預約
        }

        // 取得狀態文字
        function getStatusText(status) {
            const statusMap = {
                'pending': '待確認',
                'confirmed': '已確認',
                'completed': '已完成',
                'cancelled': '已取消'
            };
            return statusMap[status] || status;
        }

        // 取得會員等級文字
        function getMemberLevelText(level) {
            const levelMap = {
                1: '一般會員',
                2: '青銅會員',
                3: '鑽石會員',
                4: '至尊會員',
                5: '皇家至尊'
            };
            return levelMap[level] || '未知等級';
        }

        // 格式化日期時間
        function formatDateTime(dateTimeStr) {
            const date = new Date(dateTimeStr);
            return date.toLocaleString('zh-TW', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit'
            });
        }

        // 取得操作按鈕
        function getActionButtons(appointment) {
            const now = new Date();
            const appointmentTime = new Date(appointment.appointment_datetime);
            
            let buttons = [];
            
            if (appointment.status === 'pending') {
                buttons.push(`
                    <button class="btn btn-success btn-sm me-2" onclick="confirmAppointment(${appointment.id})">
                        <i class="fas fa-check"></i> 確認
                    </button>
                    <button class="btn btn-danger btn-sm" onclick="cancelAppointment(${appointment.id})">
                        <i class="fas fa-times"></i> 取消
                    </button>
                `);
            } else if (appointment.status === 'confirmed') {
                if (appointmentTime > now) {
                    buttons.push(`
                        <button class="btn btn-warning btn-sm me-2" onclick="rescheduleAppointment(${appointment.id})">
                            <i class="fas fa-calendar-alt"></i> 改期
                        </button>
                        <button class="btn btn-danger btn-sm" onclick="cancelAppointment(${appointment.id})">
                            <i class="fas fa-times"></i> 取消
                        </button>
                    `);
                } else {
                    buttons.push(`
                        <button class="btn btn-primary btn-sm" onclick="completeAppointment(${appointment.id})">
                            <i class="fas fa-check-circle"></i> 標記完成
                        </button>
                    `);
                }
            }
            
            return buttons.join('');
        }

        // 篩選預約
        function filterAppointments() {
            const statusFilter = document.getElementById('statusFilter').value;
            const moduleFilter = document.getElementById('moduleFilter').value;
            const dateFilter = document.getElementById('dateFilter').value;
            const searchInput = document.getElementById('searchInput').value.toLowerCase();
            const sortOrder = document.getElementById('sortOrder').value;

            filteredAppointments = appointments.filter(appointment => {
                const matchStatus = !statusFilter || appointment.status === statusFilter;
                const matchModule = !moduleFilter || appointment.module_id.toString() === moduleFilter;
                const matchDate = !dateFilter || matchDateFilter(appointment, dateFilter);
                const matchSearch = !searchInput || 
                    appointment.member.name.toLowerCase().includes(searchInput) ||
                    appointment.title.toLowerCase().includes(searchInput) ||
                    appointment.description.toLowerCase().includes(searchInput);

                return matchStatus && matchModule && matchDate && matchSearch;
            });

            // 排序
            filteredAppointments.sort((a, b) => {
                switch (sortOrder) {
                    case 'datetime_asc':
                        return new Date(a.appointment_datetime) - new Date(b.appointment_datetime);
                    case 'datetime_desc':
                        return new Date(b.appointment_datetime) - new Date(a.appointment_datetime);
                    case 'created_desc':
                        return new Date(b.created_at) - new Date(a.created_at);
                    case 'status':
                        return a.status.localeCompare(b.status);
                    default:
                        return new Date(b.appointment_datetime) - new Date(a.appointment_datetime);
                }
            });

            renderAppointments();
        }

        // 日期篩選匹配
        function matchDateFilter(appointment, filter) {
            const appointmentDate = new Date(appointment.appointment_datetime);
            const now = new Date();
            
            switch (filter) {
                case 'today':
                    return appointmentDate.toDateString() === now.toDateString();
                case 'tomorrow':
                    const tomorrow = new Date(now);
                    tomorrow.setDate(tomorrow.getDate() + 1);
                    return appointmentDate.toDateString() === tomorrow.toDateString();
                case 'week':
                    const weekStart = new Date(now);
                    weekStart.setDate(now.getDate() - now.getDay());
                    const weekEnd = new Date(weekStart);
                    weekEnd.setDate(weekStart.getDate() + 6);
                    return appointmentDate >= weekStart && appointmentDate <= weekEnd;
                case 'month':
                    return appointmentDate.getMonth() === now.getMonth() && 
                           appointmentDate.getFullYear() === now.getFullYear();
                default:
                    return true;
            }
        }

        // 查看預約詳情
        function viewAppointmentDetails(appointmentId) {
            const appointment = appointments.find(a => a.id === appointmentId);
            if (!appointment) return;

            const detailsHtml = `
                <div class="row">
                    <div class="col-md-6">
                        <h6>基本資訊</h6>
                        <table class="table table-sm">
                            <tr><td>預約主題</td><td>${appointment.title}</td></tr>
                            <tr><td>諮詢模組</td><td>${appointment.module.name}</td></tr>
                            <tr><td>預約時間</td><td>${formatDateTime(appointment.appointment_datetime)}</td></tr>
                            <tr><td>預約時長</td><td>${appointment.duration_minutes} 分鐘</td></tr>
                            <tr><td>狀態</td><td><span class="status-badge status-${appointment.status}">${getStatusText(appointment.status)}</span></td></tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <h6>會員資訊</h6>
                        <table class="table table-sm">
                            <tr><td>姓名</td><td>${appointment.member.name}</td></tr>
                            <tr><td>帳號</td><td>${appointment.member.uid}</td></tr>
                            <tr><td>會員等級</td><td>${getMemberLevelText(appointment.member.level)}</td></tr>
                            <tr><td>電話</td><td>${appointment.member.phone || '未提供'}</td></tr>
                        </table>
                    </div>
                </div>
                
                <h6>預約說明</h6>
                <p>${appointment.description}</p>
                
                ${appointment.notes ? `<h6>備註</h6><p>${appointment.notes}</p>` : ''}
                
                ${appointment.zoom_join_url ? `
                    <h6>會議資訊</h6>
                    <div class="zoom-info">
                        <i class="fas fa-video"></i> Zoom 會議連結: 
                        <a href="${appointment.zoom_join_url}" target="_blank">${appointment.zoom_join_url}</a>
                    </div>
                ` : ''}
                
                <h6>時間記錄</h6>
                <table class="table table-sm">
                    <tr><td>建立時間</td><td>${formatDateTime(appointment.created_at)}</td></tr>
                    ${appointment.confirmed_at ? `<tr><td>確認時間</td><td>${formatDateTime(appointment.confirmed_at)}</td></tr>` : ''}
                    ${appointment.completed_at ? `<tr><td>完成時間</td><td>${formatDateTime(appointment.completed_at)}</td></tr>` : ''}
                    ${appointment.cancelled_at ? `<tr><td>取消時間</td><td>${formatDateTime(appointment.cancelled_at)}</td></tr>` : ''}
                </table>
            `;

            document.getElementById('appointmentDetails').innerHTML = detailsHtml;
            
            // 設置操作按鈕
            const actionsHtml = getActionButtons(appointment);
            document.getElementById('appointmentActions').innerHTML = actionsHtml;
            
            appointmentModal.show();
        }

        // 確認預約
        function confirmAppointment(appointmentId) {
            currentAction = { type: 'confirm', id: appointmentId };
            document.getElementById('confirmTitle').textContent = '確認預約';
            document.getElementById('confirmMessage').textContent = '確定要確認這個預約嗎？';
            document.getElementById('confirmForm').style.display = 'block';
            document.getElementById('zoomSection').style.display = 'block';
            document.getElementById('confirmActionBtn').textContent = '確認預約';
            confirmModal.show();
        }

        // 取消預約
        function cancelAppointment(appointmentId) {
            currentAction = { type: 'cancel', id: appointmentId };
            document.getElementById('confirmTitle').textContent = '取消預約';
            document.getElementById('confirmMessage').textContent = '確定要取消這個預約嗎？';
            document.getElementById('confirmForm').style.display = 'block';
            document.getElementById('zoomSection').style.display = 'none';
            document.getElementById('confirmActionBtn').textContent = '取消預約';
            confirmModal.show();
        }

        // 完成預約
        function completeAppointment(appointmentId) {
            currentAction = { type: 'complete', id: appointmentId };
            document.getElementById('confirmTitle').textContent = '標記完成';
            document.getElementById('confirmMessage').textContent = '確定要將這個預約標記為已完成嗎？';
            document.getElementById('confirmForm').style.display = 'block';
            document.getElementById('zoomSection').style.display = 'none';
            document.getElementById('confirmActionBtn').textContent = '標記完成';
            confirmModal.show();
        }

        // 執行確認的操作
        document.getElementById('confirmActionBtn').addEventListener('click', function() {
            if (!currentAction) return;

            const note = document.getElementById('actionNote').value;
            const zoomUrl = document.getElementById('zoomUrl').value;

            const requestData = { note };
            if (currentAction.type === 'confirm' && zoomUrl) {
                requestData.zoom_join_url = zoomUrl;
            }

            const endpoint = `/api/admin/v2/appointments/${currentAction.id}/${currentAction.type}`;

            fetch(endpoint, {
                method: 'PATCH',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(requestData)
            })
            .then(response => response.json())
            .then(data => {
                if (data.msg.includes('成功')) {
                    confirmModal.hide();
                    appointmentModal.hide();
                    loadAppointments();
                    alert('操作成功！');
                } else {
                    alert('操作失敗: ' + data.error);
                }
            })
            .catch(error => {
                alert('操作失敗: ' + error.message);
            });
        });

        // 匯出預約資料
        function exportAppointments() {
            const params = new URLSearchParams();
            
            // 添加當前篩選條件
            const statusFilter = document.getElementById('statusFilter').value;
            const moduleFilter = document.getElementById('moduleFilter').value;
            const dateFilter = document.getElementById('dateFilter').value;
            
            if (statusFilter) params.append('status', statusFilter);
            if (moduleFilter) params.append('module_id', moduleFilter);
            if (dateFilter) params.append('date_filter', dateFilter);

            window.open(`/api/admin/v2/appointments/export?${params}`, '_blank');
        }

        // 更新統計
        function updateStatistics() {
            const pending = appointments.filter(a => a.status === 'pending').length;
            const confirmed = appointments.filter(a => a.status === 'confirmed').length;

            const today = new Date().toDateString();
            const todayAppointments = appointments.filter(a =>
                new Date(a.appointment_datetime).toDateString() === today
            ).length;

            document.getElementById('pendingCount').textContent = pending;
            document.getElementById('confirmedCount').textContent = confirmed;
            document.getElementById('todayCount').textContent = todayAppointments;
        }
    </script>
</body>
</html>
{{end}}
