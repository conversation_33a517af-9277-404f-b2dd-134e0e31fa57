package services

import (
	"bytes"
	"fmt"
	"html/template"
	"net/smtp"
	"os"

	. "cx/app/models"
)

// EmailService 郵件服務
type EmailService struct {
	smtpHost     string
	smtpPort     string
	smtpUser     string
	smtpPassword string
	fromName     string
	fromEmail    string
}

// NewEmailService 建立郵件服務
func NewEmailService() *EmailService {
	return &EmailService{
		smtpHost:     getEnv("SMTP_HOST", "smtp.gmail.com"),
		smtpPort:     getEnv("SMTP_PORT", "587"),
		smtpUser:     getEnv("SMTP_USER", ""),
		smtpPassword: getEnv("SMTP_PASSWORD", ""),
		fromName:     getEnv("MAIL_FROM_NAME", "G.R.A.C.E 優雅學院"),
		fromEmail:    getEnv("MAIL_FROM_EMAIL", "<EMAIL>"),
	}
}

// EmailTemplate 郵件模板
type EmailTemplate struct {
	Subject  string
	HTMLBody string
	TextBody string
}

// SendEmail 發送純文字郵件
func (s *EmailService) SendEmail(to, subject, body string) error {
	return s.sendMail(to, subject, body, "")
}

// SendHTMLEmail 發送 HTML 郵件
func (s *EmailService) SendHTMLEmail(to, subject, htmlBody string) error {
	return s.sendMail(to, subject, "", htmlBody)
}

// SendTemplateEmail 發送模板郵件
func (s *EmailService) SendTemplateEmail(to string, templateName string, data interface{}) error {
	template, err := s.getEmailTemplate(templateName, data)
	if err != nil {
		return fmt.Errorf("取得郵件模板失敗: %w", err)
	}

	return s.sendMail(to, template.Subject, template.TextBody, template.HTMLBody)
}

// sendMail 發送郵件的核心方法
func (s *EmailService) sendMail(to, subject, textBody, htmlBody string) error {
	if s.smtpUser == "" || s.smtpPassword == "" {
		return fmt.Errorf("SMTP 配置不完整")
	}

	// 建立郵件內容
	var body bytes.Buffer

	// 郵件標頭
	body.WriteString(fmt.Sprintf("From: %s <%s>\r\n", s.fromName, s.fromEmail))
	body.WriteString(fmt.Sprintf("To: %s\r\n", to))
	body.WriteString(fmt.Sprintf("Subject: %s\r\n", subject))
	body.WriteString("MIME-Version: 1.0\r\n")

	if htmlBody != "" {
		// HTML 郵件
		body.WriteString("Content-Type: multipart/alternative; boundary=\"boundary123\"\r\n\r\n")

		if textBody != "" {
			body.WriteString("--boundary123\r\n")
			body.WriteString("Content-Type: text/plain; charset=UTF-8\r\n\r\n")
			body.WriteString(textBody)
			body.WriteString("\r\n\r\n")
		}

		body.WriteString("--boundary123\r\n")
		body.WriteString("Content-Type: text/html; charset=UTF-8\r\n\r\n")
		body.WriteString(htmlBody)
		body.WriteString("\r\n\r\n--boundary123--")
	} else {
		// 純文字郵件
		body.WriteString("Content-Type: text/plain; charset=UTF-8\r\n\r\n")
		body.WriteString(textBody)
	}

	// SMTP 認證
	auth := smtp.PlainAuth("", s.smtpUser, s.smtpPassword, s.smtpHost)

	// 發送郵件
	err := smtp.SendMail(
		s.smtpHost+":"+s.smtpPort,
		auth,
		s.fromEmail,
		[]string{to},
		body.Bytes(),
	)

	if err != nil {
		return fmt.Errorf("發送郵件失敗: %w", err)
	}

	return nil
}

// getEmailTemplate 取得郵件模板
func (s *EmailService) getEmailTemplate(templateName string, data interface{}) (*EmailTemplate, error) {
	switch templateName {
	case "appointment_confirmation":
		return s.getAppointmentConfirmationTemplate(data)
	case "appointment_reminder":
		return s.getAppointmentReminderTemplate(data)
	case "assignment_review":
		return s.getAssignmentReviewTemplate(data)
	case "appointment_cancelled":
		return s.getAppointmentCancelledTemplate(data)
	default:
		return nil, fmt.Errorf("未知的郵件模板: %s", templateName)
	}
}

// getAppointmentConfirmationTemplate 預約確認郵件模板
func (s *EmailService) getAppointmentConfirmationTemplate(data interface{}) (*EmailTemplate, error) {
	appointmentData, ok := data.(map[string]interface{})
	if !ok {
		return nil, fmt.Errorf("無效的預約資料")
	}

	appointment := appointmentData["appointment"].(*ConsultationAppointment)
	member := appointmentData["member"].(*Member)

	subject := fmt.Sprintf("預約確認 - %s", appointment.Title)

	htmlTemplate := `
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: #007bff; color: white; padding: 20px; text-align: center; }
        .content { padding: 20px; background: #f9f9f9; }
        .appointment-info { background: white; padding: 15px; margin: 10px 0; border-radius: 5px; }
        .footer { text-align: center; padding: 20px; color: #666; font-size: 12px; }
        .button { display: inline-block; padding: 10px 20px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>預約確認通知</h1>
        </div>
        <div class="content">
            <p>親愛的 {{.MemberName}} 您好，</p>
            <p>您的諮詢預約已確認，詳細資訊如下：</p>
            
            <div class="appointment-info">
                <h3>預約資訊</h3>
                <p><strong>預約主題：</strong>{{.Title}}</p>
                <p><strong>預約時間：</strong>{{.DateTime}}</p>
                <p><strong>預約時長：</strong>{{.Duration}} 分鐘</p>
                <p><strong>諮詢模組：</strong>{{.ModuleName}}</p>
                {{if .ZoomURL}}
                <p><strong>會議連結：</strong><a href="{{.ZoomURL}}" class="button">加入 Zoom 會議</a></p>
                {{end}}
            </div>
            
            <p><strong>預約說明：</strong></p>
            <p>{{.Description}}</p>
            
            {{if .Notes}}
            <p><strong>備註：</strong></p>
            <p>{{.Notes}}</p>
            {{end}}
            
            <p>如有任何問題或需要修改預約，請提前 24 小時聯繫我們。</p>
        </div>
        <div class="footer">
            <p>G.R.A.C.E 優雅學院<br>
            Email: <EMAIL></p>
        </div>
    </div>
</body>
</html>`

	textBody := fmt.Sprintf(`
親愛的 %s 您好，

您的諮詢預約已確認：

預約主題：%s
預約時間：%s
預約時長：%d 分鐘
諮詢模組：%s

預約說明：%s

%s

如有任何問題，請聯繫我們。

G.R.A.C.E 優雅學院
Email: <EMAIL>
`,
		member.Name,
		appointment.Title,
		appointment.AppointmentDatetime.Format("2006-01-02 15:04"),
		appointment.DurationMinutes,
		appointment.Module.Name,
		appointment.Description,
		func() string {
			if appointment.ZoomJoinURL != "" {
				return fmt.Sprintf("會議連結：%s", appointment.ZoomJoinURL)
			}
			return ""
		}(),
	)

	// 渲染 HTML 模板
	tmpl, err := template.New("email").Parse(htmlTemplate)
	if err != nil {
		return nil, err
	}

	templateData := map[string]interface{}{
		"MemberName":  member.Name,
		"Title":       appointment.Title,
		"DateTime":    appointment.AppointmentDatetime.Format("2006-01-02 15:04"),
		"Duration":    appointment.DurationMinutes,
		"ModuleName":  appointment.Module.Name,
		"Description": appointment.Description,
		"Notes":       appointment.Notes,
		"ZoomURL":     appointment.ZoomJoinURL,
	}

	var htmlBuffer bytes.Buffer
	if err := tmpl.Execute(&htmlBuffer, templateData); err != nil {
		return nil, err
	}

	return &EmailTemplate{
		Subject:  subject,
		HTMLBody: htmlBuffer.String(),
		TextBody: textBody,
	}, nil
}

// getAppointmentReminderTemplate 預約提醒郵件模板
func (s *EmailService) getAppointmentReminderTemplate(data interface{}) (*EmailTemplate, error) {
	appointmentData, ok := data.(map[string]interface{})
	if !ok {
		return nil, fmt.Errorf("無效的預約資料")
	}

	appointment := appointmentData["appointment"].(*ConsultationAppointment)
	member := appointmentData["member"].(*Member)

	subject := fmt.Sprintf("預約提醒 - %s", appointment.Title)

	textBody := fmt.Sprintf(`
親愛的 %s 您好，

提醒您即將開始的諮詢預約：

預約主題：%s
預約時間：%s
會議連結：%s

請準時參加。

G.R.A.C.E 優雅學院
`,
		member.Name,
		appointment.Title,
		appointment.AppointmentDatetime.Format("2006-01-02 15:04"),
		appointment.ZoomJoinURL,
	)

	return &EmailTemplate{
		Subject:  subject,
		TextBody: textBody,
	}, nil
}

// getAssignmentReviewTemplate 作業審核郵件模板
func (s *EmailService) getAssignmentReviewTemplate(data interface{}) (*EmailTemplate, error) {
	reviewData, ok := data.(map[string]interface{})
	if !ok {
		return nil, fmt.Errorf("無效的審核資料")
	}

	submission := reviewData["submission"].(*MemberAssignmentSubmission)
	member := reviewData["member"].(*Member)

	var subject, textBody string

	if submission.Status == SubmissionStatusApproved {
		subject = "作業審核通過"
		textBody = fmt.Sprintf(`
親愛的 %s 您好，

您提交的作業已通過審核：

作業標題：%s
審核結果：通過
審核意見：%s

您現在可以進行下一步的預約。

G.R.A.C.E 優雅學院
`,
			member.Name,
			submission.Assignment.Title,
			submission.ReviewNote,
		)
	} else {
		subject = "作業需要修改"
		textBody = fmt.Sprintf(`
親愛的 %s 您好，

您提交的作業需要修改：

作業標題：%s
審核結果：需要修改
審核意見：%s

請根據意見修改後重新提交。

G.R.A.C.E 優雅學院
`,
			member.Name,
			submission.Assignment.Title,
			submission.ReviewNote,
		)
	}

	return &EmailTemplate{
		Subject:  subject,
		TextBody: textBody,
	}, nil
}

// getAppointmentCancelledTemplate 預約取消郵件模板
func (s *EmailService) getAppointmentCancelledTemplate(data interface{}) (*EmailTemplate, error) {
	appointmentData, ok := data.(map[string]interface{})
	if !ok {
		return nil, fmt.Errorf("無效的預約資料")
	}

	appointment := appointmentData["appointment"].(*ConsultationAppointment)
	member := appointmentData["member"].(*Member)
	reason := appointmentData["reason"].(string)

	subject := fmt.Sprintf("預約取消通知 - %s", appointment.Title)

	textBody := fmt.Sprintf(`
親愛的 %s 您好，

您的諮詢預約已被取消：

預約主題：%s
預約時間：%s
取消原因：%s

如有任何問題，請聯繫我們。

G.R.A.C.E 優雅學院
`,
		member.Name,
		appointment.Title,
		appointment.AppointmentDatetime.Format("2006-01-02 15:04"),
		reason,
	)

	return &EmailTemplate{
		Subject:  subject,
		TextBody: textBody,
	}, nil
}

// getEnv 取得環境變數
func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}
