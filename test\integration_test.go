package test

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/suite"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"

	"cx/app/controllers"
	. "cx/app/models"
)

// IntegrationTestSuite 整合測試套件
type IntegrationTestSuite struct {
	suite.Suite
	db     *gorm.DB
	router *gin.Engine

	// 測試資料
	testMember     *Member
	testModule     *ConsultationModule
	testAssignment *ConsultationAssignment
}

// SetupSuite 設置測試套件
func (suite *IntegrationTestSuite) SetupSuite() {
	// 連接測試資料庫
	dsn := "root:forwork0926@tcp(localhost:3306)/grace_test?parseTime=True&loc=Local&charset=utf8mb4&collation=utf8mb4_unicode_ci"
	db, err := gorm.Open(mysql.Open(dsn), &gorm.Config{})
	suite.Require().NoError(err)

	// 自動遷移
	err = db.AutoMigrate(
		&Member{},
		&ConsultationModule{},
		&ConsultationAssignment{},
		&MemberAssignmentSubmission{},
		&ConsultationAppointment{},
		&ConsultationTimeSlot{},
		&ConsultationEligibilityRule{},
		&FileUpload{},
	)
	suite.Require().NoError(err)

	suite.db = db

	// 設置路由
	gin.SetMode(gin.TestMode)
	suite.router = gin.New()

	// 設置控制器
	consultationController := controllers.NewConsultationV2Controller(db)
	adminController := controllers.NewAdminConsultationV2Controller(db)

	// 設置路由
	api := suite.router.Group("/api/v2")
	{
		api.GET("/modules", consultationController.GetModules)
		api.POST("/assignments/submit", consultationController.SubmitAssignment)
		api.POST("/appointments", consultationController.BookAppointment)
		api.GET("/appointments", consultationController.GetMyAppointments)
	}

	admin := suite.router.Group("/api/admin/v2")
	{
		admin.GET("/modules", adminController.GetModules)
		admin.POST("/modules", adminController.CreateModule)
		admin.GET("/submissions", adminController.GetSubmissions)
		admin.PATCH("/submissions/:id/review", adminController.ReviewSubmission)
	}
}

// SetupTest 每個測試前的設置
func (suite *IntegrationTestSuite) SetupTest() {
	// 清理測試資料
	suite.db.Exec("DELETE FROM consultation_appointments")
	suite.db.Exec("DELETE FROM member_assignment_submissions")
	suite.db.Exec("DELETE FROM consultation_assignments")
	suite.db.Exec("DELETE FROM consultation_modules")
	suite.db.Exec("DELETE FROM members WHERE id > 1000") // 保留系統資料

	// 建立測試會員
	suite.testMember = &Member{
		ID:     1001,
		Name:   "測試會員",
		Uid:    "<EMAIL>",
		Status: "Y",
		Level:  3,
	}
	suite.db.Create(suite.testMember)

	// 建立測試模組
	suite.testModule = &ConsultationModule{
		Name:        "測試諮詢模組",
		Description: "這是一個測試用的諮詢模組",
		Category:    CategoryAdvancedGuidance,
		IsActive:    true,
		SortOrder:   1,
		CreatedByID: 1,
	}
	suite.db.Create(suite.testModule)

	// 建立測試作業
	suite.testAssignment = &ConsultationAssignment{
		ModuleID:    suite.testModule.ID,
		Title:       "測試作業",
		Description: "請完成這個測試作業",
		IsRequired:  true,
		SortOrder:   1,
		CreatedByID: 1,
	}
	suite.db.Create(suite.testAssignment)
}

// TestCompleteWorkflow 測試完整的工作流程
func (suite *IntegrationTestSuite) TestCompleteWorkflow() {
	// 1. 取得模組列表
	suite.T().Log("步驟 1: 取得模組列表")
	w := httptest.NewRecorder()
	req, _ := http.NewRequest("GET", "/api/v2/modules", nil)
	suite.router.ServeHTTP(w, req)

	suite.Equal(http.StatusOK, w.Code)

	var moduleResponse struct {
		Msg  string               `json:"msg"`
		Data []ConsultationModule `json:"data"`
	}
	err := json.Unmarshal(w.Body.Bytes(), &moduleResponse)
	suite.NoError(err)
	suite.Equal("取得模組列表成功", moduleResponse.Msg)
	suite.Len(moduleResponse.Data, 1)

	// 2. 提交作業
	suite.T().Log("步驟 2: 提交作業")
	submissionData := map[string]interface{}{
		"assignment_id": suite.testAssignment.ID,
		"member_id":     suite.testMember.ID,
		"content":       "這是我的作業內容",
		"file_paths":    []string{},
	}

	jsonData, _ := json.Marshal(submissionData)
	w = httptest.NewRecorder()
	req, _ = http.NewRequest("POST", "/api/v2/assignments/submit", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")
	suite.router.ServeHTTP(w, req)

	suite.Equal(http.StatusOK, w.Code)

	var submissionResponse struct {
		Msg  string                     `json:"msg"`
		Data MemberAssignmentSubmission `json:"data"`
	}
	err = json.Unmarshal(w.Body.Bytes(), &submissionResponse)
	suite.NoError(err)
	suite.Equal("作業提交成功", submissionResponse.Msg)

	submissionID := submissionResponse.Data.ID

	// 3. 管理員審核作業
	suite.T().Log("步驟 3: 管理員審核作業")
	reviewData := map[string]interface{}{
		"status":      "approved",
		"review_note": "作業完成得很好",
	}

	jsonData, _ = json.Marshal(reviewData)
	w = httptest.NewRecorder()
	req, _ = http.NewRequest("PATCH", fmt.Sprintf("/api/admin/v2/submissions/%d/review", submissionID), bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")
	suite.router.ServeHTTP(w, req)

	suite.Equal(http.StatusOK, w.Code)

	// 4. 預約諮詢
	suite.T().Log("步驟 4: 預約諮詢")
	appointmentData := map[string]interface{}{
		"module_id":            suite.testModule.ID,
		"member_id":            suite.testMember.ID,
		"title":                "測試預約",
		"description":          "這是一個測試預約",
		"appointment_datetime": time.Now().Add(24 * time.Hour).Format("2006-01-02 15:04:05"),
		"duration_minutes":     60,
	}

	jsonData, _ = json.Marshal(appointmentData)
	w = httptest.NewRecorder()
	req, _ = http.NewRequest("POST", "/api/v2/appointments", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")
	suite.router.ServeHTTP(w, req)

	suite.Equal(http.StatusOK, w.Code)

	var appointmentResponse struct {
		Msg  string                  `json:"msg"`
		Data ConsultationAppointment `json:"data"`
	}
	err = json.Unmarshal(w.Body.Bytes(), &appointmentResponse)
	suite.NoError(err)
	suite.Equal("預約建立成功", appointmentResponse.Msg)

	// 5. 查看我的預約
	suite.T().Log("步驟 5: 查看我的預約")
	w = httptest.NewRecorder()
	req, _ = http.NewRequest("GET", fmt.Sprintf("/api/v2/appointments?member_id=%d", suite.testMember.ID), nil)
	suite.router.ServeHTTP(w, req)

	suite.Equal(http.StatusOK, w.Code)

	var appointmentsResponse struct {
		Msg  string                    `json:"msg"`
		Data []ConsultationAppointment `json:"data"`
	}
	err = json.Unmarshal(w.Body.Bytes(), &appointmentsResponse)
	suite.NoError(err)
	suite.Equal("取得預約列表成功", appointmentsResponse.Msg)
	suite.Len(appointmentsResponse.Data, 1)
}

// TestModuleManagement 測試模組管理
func (suite *IntegrationTestSuite) TestModuleManagement() {
	// 建立新模組
	moduleData := map[string]interface{}{
		"name":        "新測試模組",
		"description": "這是一個新的測試模組",
		"category":    "basic_guidance",
		"is_active":   true,
		"sort_order":  2,
	}

	jsonData, _ := json.Marshal(moduleData)
	w := httptest.NewRecorder()
	req, _ := http.NewRequest("POST", "/api/admin/v2/modules", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")
	suite.router.ServeHTTP(w, req)

	suite.Equal(http.StatusOK, w.Code)

	// 取得模組列表
	w = httptest.NewRecorder()
	req, _ = http.NewRequest("GET", "/api/admin/v2/modules", nil)
	suite.router.ServeHTTP(w, req)

	suite.Equal(http.StatusOK, w.Code)

	var response struct {
		Msg  string `json:"msg"`
		Data struct {
			Modules []ConsultationModule `json:"modules"`
			Total   int64                `json:"total"`
		} `json:"data"`
	}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	suite.NoError(err)
	suite.Equal("取得模組列表成功", response.Msg)
	suite.Equal(int64(2), response.Data.Total) // 原有1個 + 新建1個
}

// TestAssignmentSubmissionFlow 測試作業提交流程
func (suite *IntegrationTestSuite) TestAssignmentSubmissionFlow() {
	// 提交作業
	submissionData := map[string]interface{}{
		"assignment_id": suite.testAssignment.ID,
		"member_id":     suite.testMember.ID,
		"content":       "詳細的作業內容",
		"file_paths":    []string{},
	}

	jsonData, _ := json.Marshal(submissionData)
	w := httptest.NewRecorder()
	req, _ := http.NewRequest("POST", "/api/v2/assignments/submit", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")
	suite.router.ServeHTTP(w, req)

	suite.Equal(http.StatusOK, w.Code)

	// 檢查資料庫中的提交記錄
	var submission MemberAssignmentSubmission
	err := suite.db.Where("assignment_id = ? AND member_id = ?",
		suite.testAssignment.ID, suite.testMember.ID).First(&submission).Error
	suite.NoError(err)
	suite.Equal("詳細的作業內容", submission.Content)
	suite.Equal(SubmissionStatusSubmitted, submission.Status)
}

// TestErrorHandling 測試錯誤處理
func (suite *IntegrationTestSuite) TestErrorHandling() {
	// 測試無效的模組ID
	appointmentData := map[string]interface{}{
		"module_id":            99999, // 不存在的模組ID
		"member_id":            suite.testMember.ID,
		"title":                "無效預約",
		"appointment_datetime": time.Now().Add(24 * time.Hour).Format("2006-01-02 15:04:05"),
		"duration_minutes":     60,
	}

	jsonData, _ := json.Marshal(appointmentData)
	w := httptest.NewRecorder()
	req, _ := http.NewRequest("POST", "/api/v2/appointments", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")
	suite.router.ServeHTTP(w, req)

	suite.Equal(http.StatusBadRequest, w.Code)

	// 測試無效的作業提交
	submissionData := map[string]interface{}{
		"assignment_id": 99999, // 不存在的作業ID
		"member_id":     suite.testMember.ID,
		"content":       "",
	}

	jsonData, _ = json.Marshal(submissionData)
	w = httptest.NewRecorder()
	req, _ = http.NewRequest("POST", "/api/v2/assignments/submit", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")
	suite.router.ServeHTTP(w, req)

	suite.Equal(http.StatusBadRequest, w.Code)
}

// TearDownTest 每個測試後的清理
func (suite *IntegrationTestSuite) TearDownTest() {
	// 清理測試資料
	suite.db.Exec("DELETE FROM consultation_appointments")
	suite.db.Exec("DELETE FROM member_assignment_submissions")
	suite.db.Exec("DELETE FROM consultation_assignments")
	suite.db.Exec("DELETE FROM consultation_modules")
	suite.db.Exec("DELETE FROM members WHERE id > 1000")
}

// TearDownSuite 測試套件結束後的清理
func (suite *IntegrationTestSuite) TearDownSuite() {
	// 關閉資料庫連接
	if suite.db != nil {
		sqlDB, _ := suite.db.DB()
		sqlDB.Close()
	}
}

// TestIntegrationTestSuite 執行整合測試套件
func TestIntegrationTestSuite(t *testing.T) {
	suite.Run(t, new(IntegrationTestSuite))
}
