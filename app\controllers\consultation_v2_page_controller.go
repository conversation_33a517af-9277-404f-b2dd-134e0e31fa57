package controllers

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"

	. "cx/app/models"
	"cx/app/services"
)

// ConsultationV2PageController 諮詢系統頁面控制器
type ConsultationV2PageController struct {
	db      *gorm.DB
	service *services.ConsultationV2Service
}

// NewConsultationV2PageController 建立諮詢系統頁面控制器
func NewConsultationV2PageController(db *gorm.DB) *ConsultationV2PageController {
	return &ConsultationV2PageController{
		db:      db,
		service: services.NewConsultationV2Service(db),
	}
}

// ShowModules 顯示模組列表頁面
func (c *ConsultationV2PageController) ShowModules(ctx *gin.Context) {
	categoryParam := ctx.Query("category")
	var category *ConsultationModuleCategory

	if categoryParam != "" {
		cat := ConsultationModuleCategory(categoryParam)
		category = &cat
	}

	// 取得可用的模組
	modules, err := c.service.GetActiveModules(category)
	if err != nil {
		ctx.HTML(http.StatusInternalServerError, "error", gin.H{
			"Title": "錯誤",
			"Error": "載入模組失敗",
		})
		return
	}

	ctx.HTML(http.StatusOK, "consultation_v2/modules", gin.H{
		"Title":   "諮詢模組",
		"Modules": modules,
	})
}

// ShowAssignmentSubmit 顯示作業提交頁面
func (c *ConsultationV2PageController) ShowAssignmentSubmit(ctx *gin.Context) {
	assignmentIDStr := ctx.Param("id")
	assignmentID, err := strconv.ParseUint(assignmentIDStr, 10, 32)
	if err != nil {
		ctx.HTML(http.StatusBadRequest, "error", gin.H{
			"Title": "錯誤",
			"Error": "無效的作業ID",
		})
		return
	}

	// 取得會員ID
	memberID, exists := ctx.Get("member_id")
	if !exists {
		ctx.Redirect(http.StatusFound, "/login")
		return
	}

	// 取得作業資訊
	var assignment ConsultationAssignment
	if err := c.db.Preload("Module").First(&assignment, assignmentID).Error; err != nil {
		ctx.HTML(http.StatusNotFound, "error", gin.H{
			"Title": "錯誤",
			"Error": "作業不存在",
		})
		return
	}

	// 取得會員的提交記錄
	var submission MemberAssignmentSubmission
	submissionExists := c.db.Where("assignment_id = ? AND member_id = ?", assignmentID, memberID).
		First(&submission).Error == nil

	// 取得提交歷史
	var submissionHistory []MemberAssignmentSubmission
	c.db.Where("assignment_id = ? AND member_id = ?", assignmentID, memberID).
		Order("submitted_at DESC").Find(&submissionHistory)

	var submissionPtr *MemberAssignmentSubmission
	if submissionExists {
		submissionPtr = &submission
	}

	ctx.HTML(http.StatusOK, "consultation_v2/assignment_submit", gin.H{
		"Title":             "作業提交",
		"Assignment":        assignment,
		"Submission":        submissionPtr,
		"SubmissionHistory": submissionHistory,
	})
}

// ShowAppointmentBooking 顯示預約頁面
func (c *ConsultationV2PageController) ShowAppointmentBooking(ctx *gin.Context) {
	// 取得可用的模組
	var modules []ConsultationModule
	if err := c.db.Where("is_active = ?", true).Order("sort_order ASC").Find(&modules).Error; err != nil {
		ctx.HTML(http.StatusInternalServerError, "error", gin.H{
			"Title": "錯誤",
			"Error": "載入模組失敗",
		})
		return
	}

	ctx.HTML(http.StatusOK, "consultation_v2/appointment_booking", gin.H{
		"Title":   "預約諮詢",
		"Modules": modules,
	})
}

// ShowMyAppointments 顯示我的預約列表
func (c *ConsultationV2PageController) ShowMyAppointments(ctx *gin.Context) {
	ctx.HTML(http.StatusOK, "consultation_v2/my_appointments", gin.H{
		"Title": "我的預約",
	})
}

// ShowAppointmentDetail 顯示預約詳情頁面
func (c *ConsultationV2PageController) ShowAppointmentDetail(ctx *gin.Context) {
	appointmentIDStr := ctx.Param("id")
	appointmentID, err := strconv.ParseUint(appointmentIDStr, 10, 32)
	if err != nil {
		ctx.HTML(http.StatusBadRequest, "error", gin.H{
			"Title": "錯誤",
			"Error": "無效的預約ID",
		})
		return
	}

	// 取得會員ID
	memberID, exists := ctx.Get("member_id")
	if !exists {
		ctx.Redirect(http.StatusFound, "/login")
		return
	}

	// 取得預約資訊
	var appointment ConsultationAppointment
	if err := c.db.Preload("Module").
		Where("id = ? AND member_id = ?", appointmentID, memberID).
		First(&appointment).Error; err != nil {
		ctx.HTML(http.StatusNotFound, "error", gin.H{
			"Title": "錯誤",
			"Error": "預約不存在",
		})
		return
	}

	ctx.HTML(http.StatusOK, "consultation_v2/appointment_detail", gin.H{
		"Title":       "預約詳情",
		"Appointment": appointment,
	})
}

// ShowModuleDetail 顯示模組詳情頁面
func (c *ConsultationV2PageController) ShowModuleDetail(ctx *gin.Context) {
	moduleIDStr := ctx.Param("id")
	moduleID, err := strconv.ParseUint(moduleIDStr, 10, 32)
	if err != nil {
		ctx.HTML(http.StatusBadRequest, "error", gin.H{
			"Title": "錯誤",
			"Error": "無效的模組ID",
		})
		return
	}

	// 取得模組資訊
	var module ConsultationModule
	if err := c.db.First(&module, moduleID).Error; err != nil {
		ctx.HTML(http.StatusNotFound, "error", gin.H{
			"Title": "錯誤",
			"Error": "模組不存在",
		})
		return
	}

	// 取得模組的作業要求
	var assignments []ConsultationAssignment
	c.db.Where("module_id = ?", moduleID).
		Order("sort_order ASC, created_at ASC").
		Find(&assignments)

	// 檢查會員是否符合預約資格
	memberID, exists := ctx.Get("member_id")
	var eligible bool
	var eligibilityMessage string

	if exists {
		var reasons []string
		eligible, reasons, _ = c.service.CheckEligibility(uint(moduleID), memberID.(uint))
		if len(reasons) > 0 {
			eligibilityMessage = reasons[0] // 取第一個原因作為顯示訊息
		}
	}

	ctx.HTML(http.StatusOK, "consultation_v2/module_detail", gin.H{
		"Title":              "模組詳情",
		"Module":             module,
		"Assignments":        assignments,
		"Eligible":           eligible,
		"EligibilityMessage": eligibilityMessage,
	})
}

// ShowAssignmentList 顯示作業列表頁面
func (c *ConsultationV2PageController) ShowAssignmentList(ctx *gin.Context) {
	moduleIDStr := ctx.Param("id")
	moduleID, err := strconv.ParseUint(moduleIDStr, 10, 32)
	if err != nil {
		ctx.HTML(http.StatusBadRequest, "error", gin.H{
			"Title": "錯誤",
			"Error": "無效的模組ID",
		})
		return
	}

	// 取得模組資訊
	var module ConsultationModule
	if err := c.db.First(&module, moduleID).Error; err != nil {
		ctx.HTML(http.StatusNotFound, "error", gin.H{
			"Title": "錯誤",
			"Error": "模組不存在",
		})
		return
	}

	// 取得作業列表
	var assignments []ConsultationAssignment
	c.db.Where("module_id = ?", moduleID).
		Order("sort_order ASC, created_at ASC").
		Find(&assignments)

	// 取得會員的提交狀態
	memberID, exists := ctx.Get("member_id")
	var submissionStatus map[uint]string

	if exists {
		submissionStatus = make(map[uint]string)
		var submissions []MemberAssignmentSubmission
		c.db.Where("member_id = ?", memberID).Find(&submissions)

		for _, submission := range submissions {
			submissionStatus[submission.AssignmentID] = string(submission.Status)
		}
	}

	ctx.HTML(http.StatusOK, "consultation_v2/assignment_list", gin.H{
		"Title":            "作業列表",
		"Module":           module,
		"Assignments":      assignments,
		"SubmissionStatus": submissionStatus,
	})
}
