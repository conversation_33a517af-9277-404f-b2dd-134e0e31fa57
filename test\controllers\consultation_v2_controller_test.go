package controllers_test

import (
	"bytes"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/suite"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"

	"cx/app/controllers"
	. "cx/app/models"
)

// ConsultationV2ControllerTestSuite API 控制器測試套件
type ConsultationV2ControllerTestSuite struct {
	suite.Suite
	db         *gorm.DB
	router     *gin.Engine
	controller *controllers.ConsultationV2Controller
}

// SetupSuite 設置測試套件
func (suite *ConsultationV2ControllerTestSuite) SetupSuite() {
	// 使用測試資料庫連接
	dsn := "root:forwork0926@tcp(localhost:3306)/grace_test?parseTime=True&loc=Local&charset=utf8mb4&collation=utf8mb4_unicode_ci"
	db, err := gorm.Open(mysql.Open(dsn), &gorm.Config{})
	suite.Require().NoError(err)
	suite.Require().NotNil(db)

	// 自動遷移測試表
	err = db.AutoMigrate(
		&ConsultationModule{},
		&ConsultationAssignment{},
		&MemberAssignmentSubmission{},
		&ConsultationAppointment{},
		&ConsultationTimeSlot{},
		&ConsultationEligibilityRule{},
		&Member{},
	)
	suite.Require().NoError(err)

	suite.db = db
	suite.controller = controllers.NewConsultationV2Controller(db)

	// 設置 Gin 路由
	gin.SetMode(gin.TestMode)
	suite.router = gin.New()

	// 模擬會員認證中間件
	suite.router.Use(func(c *gin.Context) {
		c.Set("member_id", uint(1))
		c.Next()
	})

	// 設置路由
	api := suite.router.Group("/api/v2")
	{
		api.GET("/modules", suite.controller.GetModules)
		api.GET("/modules/:id/assignments", suite.controller.GetModuleAssignments)
		api.POST("/assignments/submit", suite.controller.SubmitAssignment)
		api.GET("/appointments/available-slots", suite.controller.GetAvailableSlots)
		api.POST("/appointments", suite.controller.BookAppointment)
		api.GET("/appointments", suite.controller.GetMyAppointments)
		api.PATCH("/appointments/:id/cancel", suite.controller.CancelAppointment)
	}
}

// TearDownSuite 清理測試套件
func (suite *ConsultationV2ControllerTestSuite) TearDownSuite() {
	sqlDB, _ := suite.db.DB()
	sqlDB.Close()
}

// SetupTest 每個測試前的設置
func (suite *ConsultationV2ControllerTestSuite) SetupTest() {
	// 清理測試資料
	suite.db.Exec("DELETE FROM consultation_appointments")
	suite.db.Exec("DELETE FROM member_assignment_submissions")
	suite.db.Exec("DELETE FROM consultation_assignments")
	suite.db.Exec("DELETE FROM consultation_modules")
	suite.db.Exec("DELETE FROM consultation_time_slots")
	suite.db.Exec("DELETE FROM consultation_eligibility_rules")
	suite.db.Exec("DELETE FROM members")

	// 建立測試會員
	member := &Member{
		Uid:    "<EMAIL>",
		Name:   "測試會員",
		Status: "Y",
		Level:  2,
	}
	suite.db.Create(member)
}

// TestGetModules 測試取得模組列表
func (suite *ConsultationV2ControllerTestSuite) TestGetModules() {
	// 建立測試模組
	module := &ConsultationModule{
		Name:        "測試模組",
		Description: "測試模組描述",
		Category:    CategoryAdvancedGuidance,
		IsActive:    true,
		SortOrder:   1,
		CreatedByID: 1,
	}
	suite.db.Create(module)

	// 發送請求
	req, _ := http.NewRequest("GET", "/api/v2/modules", nil)
	w := httptest.NewRecorder()
	suite.router.ServeHTTP(w, req)

	// 檢查回應
	assert.Equal(suite.T(), http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), "取得模組列表成功", response["msg"])

	data := response["data"].([]interface{})
	assert.Len(suite.T(), data, 1)

	moduleData := data[0].(map[string]interface{})
	assert.Equal(suite.T(), module.Name, moduleData["name"])
}

// TestSubmitAssignment 測試提交作業
func (suite *ConsultationV2ControllerTestSuite) TestSubmitAssignment() {
	// 建立測試資料
	module := &ConsultationModule{
		Name:        "測試模組",
		Description: "測試模組描述",
		Category:    CategoryAdvancedGuidance,
		IsActive:    true,
		SortOrder:   1,
		CreatedByID: 1,
	}
	suite.db.Create(module)

	assignment := &ConsultationAssignment{
		ModuleID:    module.ID,
		Title:       "測試作業",
		Description: "請提交相關文件",
		SortOrder:   1,
		CreatedByID: 1,
	}
	suite.db.Create(assignment)

	// 準備請求資料
	requestData := map[string]interface{}{
		"assignment_id": assignment.ID,
		"submission_data": map[string]interface{}{
			"content": "這是我的作業內容",
		},
		"files": []map[string]interface{}{},
	}

	jsonData, _ := json.Marshal(requestData)
	req, _ := http.NewRequest("POST", "/api/v2/assignments/submit", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")

	w := httptest.NewRecorder()
	suite.router.ServeHTTP(w, req)

	// 檢查回應
	assert.Equal(suite.T(), http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), "作業提交成功", response["msg"])

	// 檢查資料庫中的記錄
	var submission MemberAssignmentSubmission
	err = suite.db.Where("assignment_id = ? AND member_id = ?", assignment.ID, 1).First(&submission).Error
	assert.NoError(suite.T(), err)

	// 解析提交資料
	var submissionData map[string]interface{}
	err = json.Unmarshal([]byte(submission.SubmissionData), &submissionData)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), "這是我的作業內容", submissionData["content"])
}

// TestBookAppointment 測試預約
func (suite *ConsultationV2ControllerTestSuite) TestBookAppointment() {
	// 建立測試資料
	module := &ConsultationModule{
		Name:        "測試模組",
		Description: "測試模組描述",
		Category:    CategoryAdvancedGuidance,
		IsActive:    true,
		SortOrder:   1,
		CreatedByID: 1,
	}
	suite.db.Create(module)

	// 建立時段
	timeSlot := &ConsultationTimeSlot{
		Date:            time.Now().AddDate(0, 0, 1), // 明天
		StartTime:       time.Date(0, 1, 1, 9, 0, 0, 0, time.UTC),
		EndTime:         time.Date(0, 1, 1, 10, 0, 0, 0, time.UTC),
		IsAvailable:     true,
		MaxAppointments: 1,
		CreatedByID:     1,
	}
	suite.db.Create(timeSlot)

	appointmentTime := time.Now().AddDate(0, 0, 1).Truncate(time.Hour).Add(9 * time.Hour)

	// 準備請求資料
	requestData := map[string]interface{}{
		"module_id":            module.ID,
		"title":                "測試預約",
		"description":          "這是一個測試預約",
		"appointment_datetime": appointmentTime.Format("2006-01-02 15:04:05"),
		"duration_minutes":     60,
		"notes":                "測試備註",
	}

	jsonData, _ := json.Marshal(requestData)
	req, _ := http.NewRequest("POST", "/api/v2/appointments", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")

	w := httptest.NewRecorder()
	suite.router.ServeHTTP(w, req)

	// 檢查回應
	assert.Equal(suite.T(), http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), "預約建立成功", response["msg"])

	// 檢查資料庫中的記錄
	var appointment ConsultationAppointment
	err = suite.db.Where("module_id = ? AND member_id = ?", module.ID, 1).First(&appointment).Error
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), "測試預約", appointment.Title)
	assert.Equal(suite.T(), AppointmentStatusPending, appointment.Status)
}

// TestGetMyAppointments 測試取得我的預約
func (suite *ConsultationV2ControllerTestSuite) TestGetMyAppointments() {
	// 建立測試資料
	module := &ConsultationModule{
		Name:        "測試模組",
		Description: "測試模組描述",
		Category:    CategoryAdvancedGuidance,
		IsActive:    true,
		SortOrder:   1,
		CreatedByID: 1,
	}
	suite.db.Create(module)

	appointment := &ConsultationAppointment{
		ModuleID:            module.ID,
		MemberID:            1,
		Title:               "測試預約",
		Description:         "測試預約描述",
		AppointmentDatetime: time.Now().AddDate(0, 0, 1),
		DurationMinutes:     60,
		Status:              AppointmentStatusPending,
	}
	suite.db.Create(appointment)

	// 發送請求
	req, _ := http.NewRequest("GET", "/api/v2/appointments", nil)
	w := httptest.NewRecorder()
	suite.router.ServeHTTP(w, req)

	// 檢查回應
	assert.Equal(suite.T(), http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), "取得預約列表成功", response["msg"])

	data := response["data"].(map[string]interface{})
	appointments := data["appointments"].([]interface{})
	assert.Len(suite.T(), appointments, 1)

	appointmentData := appointments[0].(map[string]interface{})
	assert.Equal(suite.T(), appointment.Title, appointmentData["title"])
}

// TestCancelAppointment 測試取消預約
func (suite *ConsultationV2ControllerTestSuite) TestCancelAppointment() {
	// 建立測試資料
	module := &ConsultationModule{
		Name:        "測試模組",
		Description: "測試模組描述",
		Category:    CategoryAdvancedGuidance,
		IsActive:    true,
		SortOrder:   1,
		CreatedByID: 1,
	}
	suite.db.Create(module)

	appointment := &ConsultationAppointment{
		ModuleID:            module.ID,
		MemberID:            1,
		Title:               "測試預約",
		Description:         "測試預約描述",
		AppointmentDatetime: time.Now().AddDate(0, 0, 1),
		DurationMinutes:     60,
		Status:              AppointmentStatusPending,
	}
	suite.db.Create(appointment)

	// 準備請求資料
	requestData := map[string]interface{}{
		"reason": "測試取消",
	}

	jsonData, _ := json.Marshal(requestData)
	req, _ := http.NewRequest("PATCH", "/api/v2/appointments/1/cancel", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")

	w := httptest.NewRecorder()
	suite.router.ServeHTTP(w, req)

	// 檢查回應
	assert.Equal(suite.T(), http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), "取消預約成功", response["msg"])

	// 檢查資料庫中的記錄
	var updatedAppointment ConsultationAppointment
	err = suite.db.First(&updatedAppointment, appointment.ID).Error
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), AppointmentStatusCancelled, updatedAppointment.Status)
}

// 運行測試套件
func TestConsultationV2ControllerTestSuite(t *testing.T) {
	suite.Run(t, new(ConsultationV2ControllerTestSuite))
}
