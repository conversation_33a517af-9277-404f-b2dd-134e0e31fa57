package controllers

import (
	"fmt"
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"

	. "cx/app/models"
	"cx/app/services"
)

// AdminConsultationV2Controller 後台諮詢管理控制器
type AdminConsultationV2Controller struct {
	service *services.ConsultationV2Service
	db      *gorm.DB
}

// NewAdminConsultationV2Controller 建立後台諮詢管理控制器
func NewAdminConsultationV2Controller(db *gorm.DB) *AdminConsultationV2Controller {
	return &AdminConsultationV2Controller{
		service: services.NewConsultationV2Service(db),
		db:      db,
	}
}

// CreateModule 建立諮詢模組
func (c *AdminConsultationV2Controller) CreateModule(ctx *gin.Context) {
	var req services.CreateModuleRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"msg":   "請求參數錯誤",
			"error": err.Error(),
		})
		return
	}

	// 取得管理員ID
	adminID, exists := ctx.Get("admin_id")
	if !exists {
		ctx.JSON(http.StatusUnauthorized, gin.H{
			"msg": "未登入",
		})
		return
	}

	module, err := c.service.CreateModule(req, adminID.(uint))
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"msg":   "建立模組失敗",
			"error": err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"msg":  "建立模組成功",
		"data": module,
	})
}

// GetModules 取得所有模組
func (c *AdminConsultationV2Controller) GetModules(ctx *gin.Context) {
	page, _ := strconv.Atoi(ctx.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(ctx.DefaultQuery("limit", "20"))
	category := ctx.Query("category")

	offset := (page - 1) * limit

	var modules []ConsultationModule
	var total int64

	query := c.db.Model(&ConsultationModule{})
	if category != "" {
		query = query.Where("category = ?", category)
	}

	query.Count(&total)

	err := query.Offset(offset).Limit(limit).
		Order("sort_order ASC, created_at DESC").
		Find(&modules).Error

	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"msg":   "取得模組列表失敗",
			"error": err.Error(),
		})
		return
	}

	result := map[string]interface{}{
		"modules": modules,
		"total":   total,
		"page":    page,
		"limit":   limit,
	}

	ctx.JSON(http.StatusOK, gin.H{
		"msg":  "取得模組列表成功",
		"data": result,
	})
}

// UpdateModule 更新模組
func (c *AdminConsultationV2Controller) UpdateModule(ctx *gin.Context) {
	moduleIDStr := ctx.Param("id")
	moduleID, err := strconv.ParseUint(moduleIDStr, 10, 32)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"msg":   "無效的模組ID",
			"error": err.Error(),
		})
		return
	}

	var req services.CreateModuleRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"msg":   "請求參數錯誤",
			"error": err.Error(),
		})
		return
	}

	// 取得管理員ID
	adminID, exists := ctx.Get("admin_id")
	if !exists {
		ctx.JSON(http.StatusUnauthorized, gin.H{
			"msg": "未登入",
		})
		return
	}

	var module ConsultationModule
	if err := c.db.First(&module, moduleID).Error; err != nil {
		ctx.JSON(http.StatusNotFound, gin.H{
			"msg":   "模組不存在",
			"error": err.Error(),
		})
		return
	}

	// 更新模組資料
	module.Name = req.Name
	module.Description = req.Description
	module.Category = req.Category
	module.IsActive = req.IsActive
	module.SortOrder = req.SortOrder
	module.UpdatedByID.SetValid(int64(adminID.(uint)))

	if err := c.db.Save(&module).Error; err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"msg":   "更新模組失敗",
			"error": err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"msg":  "更新模組成功",
		"data": module,
	})
}

// DeleteModule 刪除模組
func (c *AdminConsultationV2Controller) DeleteModule(ctx *gin.Context) {
	moduleIDStr := ctx.Param("id")
	moduleID, err := strconv.ParseUint(moduleIDStr, 10, 32)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"msg":   "無效的模組ID",
			"error": err.Error(),
		})
		return
	}

	// 檢查是否有關聯的預約
	var appointmentCount int64
	c.db.Model(&ConsultationAppointment{}).Where("module_id = ?", moduleID).Count(&appointmentCount)
	if appointmentCount > 0 {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"msg":   "無法刪除",
			"error": "此模組已有預約記錄",
		})
		return
	}

	if err := c.db.Delete(&ConsultationModule{}, moduleID).Error; err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"msg":   "刪除模組失敗",
			"error": err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"msg": "刪除模組成功",
	})
}

// CreateAssignment 建立作業配置
func (c *AdminConsultationV2Controller) CreateAssignment(ctx *gin.Context) {
	var req services.CreateAssignmentRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"msg":   "請求參數錯誤",
			"error": err.Error(),
		})
		return
	}

	// 取得管理員ID
	adminID, exists := ctx.Get("admin_id")
	if !exists {
		ctx.JSON(http.StatusUnauthorized, gin.H{
			"msg": "未登入",
		})
		return
	}

	assignment, err := c.service.CreateAssignment(req, adminID.(uint))
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"msg":   "建立作業配置失敗",
			"error": err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"msg":  "建立作業配置成功",
		"data": assignment,
	})
}

// GetAssignments 取得作業配置列表
func (c *AdminConsultationV2Controller) GetAssignments(ctx *gin.Context) {
	page, _ := strconv.Atoi(ctx.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(ctx.DefaultQuery("limit", "20"))
	moduleIDStr := ctx.Query("module_id")

	offset := (page - 1) * limit

	var assignments []ConsultationAssignment
	var total int64

	query := c.db.Model(&ConsultationAssignment{}).Preload("Module")
	if moduleIDStr != "" {
		moduleID, err := strconv.ParseUint(moduleIDStr, 10, 32)
		if err == nil {
			query = query.Where("module_id = ?", moduleID)
		}
	}

	query.Count(&total)

	err := query.Offset(offset).Limit(limit).
		Order("module_id ASC, sort_order ASC, created_at DESC").
		Find(&assignments).Error

	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"msg":   "取得作業配置列表失敗",
			"error": err.Error(),
		})
		return
	}

	result := map[string]interface{}{
		"assignments": assignments,
		"total":       total,
		"page":        page,
		"limit":       limit,
	}

	ctx.JSON(http.StatusOK, gin.H{
		"msg":  "取得作業配置列表成功",
		"data": result,
	})
}

// GetSubmissions 取得作業提交列表
func (c *AdminConsultationV2Controller) GetSubmissions(ctx *gin.Context) {
	page, _ := strconv.Atoi(ctx.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(ctx.DefaultQuery("limit", "20"))
	assignmentIDStr := ctx.Query("assignment_id")
	status := ctx.Query("status")

	offset := (page - 1) * limit

	var submissions []MemberAssignmentSubmission
	var total int64

	query := c.db.Model(&MemberAssignmentSubmission{}).
		Preload("Assignment").
		Preload("Assignment.Module")

	if assignmentIDStr != "" {
		assignmentID, err := strconv.ParseUint(assignmentIDStr, 10, 32)
		if err == nil {
			query = query.Where("assignment_id = ?", assignmentID)
		}
	}

	if status != "" {
		query = query.Where("status = ?", status)
	}

	query.Count(&total)

	err := query.Offset(offset).Limit(limit).
		Order("submitted_at DESC, created_at DESC").
		Find(&submissions).Error

	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"msg":   "取得作業提交列表失敗",
			"error": err.Error(),
		})
		return
	}

	result := map[string]interface{}{
		"submissions": submissions,
		"total":       total,
		"page":        page,
		"limit":       limit,
	}

	ctx.JSON(http.StatusOK, gin.H{
		"msg":  "取得作業提交列表成功",
		"data": result,
	})
}

// ReviewSubmission 審核作業提交
func (c *AdminConsultationV2Controller) ReviewSubmission(ctx *gin.Context) {
	submissionIDStr := ctx.Param("id")
	submissionID, err := strconv.ParseUint(submissionIDStr, 10, 32)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"msg":   "無效的提交ID",
			"error": err.Error(),
		})
		return
	}

	var req struct {
		Status     AssignmentSubmissionStatus `json:"status" binding:"required"`
		ReviewNote string                     `json:"review_note"`
	}

	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"msg":   "請求參數錯誤",
			"error": err.Error(),
		})
		return
	}

	// 取得管理員ID
	adminID, exists := ctx.Get("admin_id")
	if !exists {
		ctx.JSON(http.StatusUnauthorized, gin.H{
			"msg": "未登入",
		})
		return
	}

	err = c.service.ReviewAssignment(uint(submissionID), req.Status, req.ReviewNote, adminID.(uint))
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"msg":   "審核作業失敗",
			"error": err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"msg": "審核作業成功",
	})
}

// CreateTimeSlot 建立時段
func (c *AdminConsultationV2Controller) CreateTimeSlot(ctx *gin.Context) {
	var req struct {
		Date            string `json:"date" binding:"required"`
		StartTime       string `json:"start_time" binding:"required"`
		EndTime         string `json:"end_time" binding:"required"`
		IsAvailable     bool   `json:"is_available"`
		MaxAppointments int    `json:"max_appointments"`
	}

	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"msg":   "請求參數錯誤",
			"error": err.Error(),
		})
		return
	}

	// 解析日期和時間
	date, err := time.Parse("2006-01-02", req.Date)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"msg":   "日期格式錯誤",
			"error": err.Error(),
		})
		return
	}

	startTime, err := time.Parse("15:04", req.StartTime)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"msg":   "開始時間格式錯誤",
			"error": err.Error(),
		})
		return
	}

	endTime, err := time.Parse("15:04", req.EndTime)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"msg":   "結束時間格式錯誤",
			"error": err.Error(),
		})
		return
	}

	// 取得管理員ID
	adminID, exists := ctx.Get("admin_id")
	if !exists {
		ctx.JSON(http.StatusUnauthorized, gin.H{
			"msg": "未登入",
		})
		return
	}

	timeSlot := &ConsultationTimeSlot{
		Date:            date,
		StartTime:       startTime,
		EndTime:         endTime,
		IsAvailable:     req.IsAvailable,
		MaxAppointments: req.MaxAppointments,
		CreatedByID:     adminID.(uint),
	}

	if timeSlot.MaxAppointments == 0 {
		timeSlot.MaxAppointments = 1
	}

	if err := c.db.Create(timeSlot).Error; err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"msg":   "建立時段失敗",
			"error": err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"msg":  "建立時段成功",
		"data": timeSlot,
	})
}

// GetTimeSlots 取得時段列表
func (c *AdminConsultationV2Controller) GetTimeSlots(ctx *gin.Context) {
	page, _ := strconv.Atoi(ctx.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(ctx.DefaultQuery("limit", "20"))
	startDate := ctx.Query("start_date")
	endDate := ctx.Query("end_date")

	offset := (page - 1) * limit

	var timeSlots []ConsultationTimeSlot
	var total int64

	query := c.db.Model(&ConsultationTimeSlot{})

	if startDate != "" {
		query = query.Where("date >= ?", startDate)
	}
	if endDate != "" {
		query = query.Where("date <= ?", endDate)
	}

	query.Count(&total)

	err := query.Offset(offset).Limit(limit).
		Order("date ASC, start_time ASC").
		Find(&timeSlots).Error

	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"msg":   "取得時段列表失敗",
			"error": err.Error(),
		})
		return
	}

	result := map[string]interface{}{
		"time_slots": timeSlots,
		"total":      total,
		"page":       page,
		"limit":      limit,
	}

	ctx.JSON(http.StatusOK, gin.H{
		"msg":  "取得時段列表成功",
		"data": result,
	})
}

// UpdateTimeSlot 更新時段
func (c *AdminConsultationV2Controller) UpdateTimeSlot(ctx *gin.Context) {
	timeSlotIDStr := ctx.Param("id")
	timeSlotID, err := strconv.ParseUint(timeSlotIDStr, 10, 32)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"msg":   "無效的時段ID",
			"error": err.Error(),
		})
		return
	}

	var req struct {
		IsAvailable     *bool `json:"is_available"`
		MaxAppointments *int  `json:"max_appointments"`
	}

	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"msg":   "請求參數錯誤",
			"error": err.Error(),
		})
		return
	}

	// 取得管理員ID
	adminID, exists := ctx.Get("admin_id")
	if !exists {
		ctx.JSON(http.StatusUnauthorized, gin.H{
			"msg": "未登入",
		})
		return
	}

	var timeSlot ConsultationTimeSlot
	if err := c.db.First(&timeSlot, timeSlotID).Error; err != nil {
		ctx.JSON(http.StatusNotFound, gin.H{
			"msg":   "時段不存在",
			"error": err.Error(),
		})
		return
	}

	// 更新時段資料
	if req.IsAvailable != nil {
		timeSlot.IsAvailable = *req.IsAvailable
	}
	if req.MaxAppointments != nil {
		timeSlot.MaxAppointments = *req.MaxAppointments
	}
	timeSlot.UpdatedByID.SetValid(int64(adminID.(uint)))

	if err := c.db.Save(&timeSlot).Error; err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"msg":   "更新時段失敗",
			"error": err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"msg":  "更新時段成功",
		"data": timeSlot,
	})
}

// DeleteTimeSlot 刪除時段
func (c *AdminConsultationV2Controller) DeleteTimeSlot(ctx *gin.Context) {
	timeSlotIDStr := ctx.Param("id")
	timeSlotID, err := strconv.ParseUint(timeSlotIDStr, 10, 32)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"msg":   "無效的時段ID",
			"error": err.Error(),
		})
		return
	}

	if err := c.db.Delete(&ConsultationTimeSlot{}, timeSlotID).Error; err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"msg":   "刪除時段失敗",
			"error": err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"msg": "刪除時段成功",
	})
}

// GetAppointments 取得預約列表
func (c *AdminConsultationV2Controller) GetAppointments(ctx *gin.Context) {
	page, _ := strconv.Atoi(ctx.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(ctx.DefaultQuery("limit", "20"))
	status := ctx.Query("status")
	moduleIDStr := ctx.Query("module_id")
	startDate := ctx.Query("start_date")
	endDate := ctx.Query("end_date")

	offset := (page - 1) * limit

	var appointments []ConsultationAppointment
	var total int64

	query := c.db.Model(&ConsultationAppointment{}).
		Preload("Module").
		Preload("Submission")

	if status != "" {
		query = query.Where("status = ?", status)
	}

	if moduleIDStr != "" {
		moduleID, err := strconv.ParseUint(moduleIDStr, 10, 32)
		if err == nil {
			query = query.Where("module_id = ?", moduleID)
		}
	}

	if startDate != "" {
		query = query.Where("appointment_datetime >= ?", startDate)
	}
	if endDate != "" {
		query = query.Where("appointment_datetime <= ?", endDate+" 23:59:59")
	}

	query.Count(&total)

	err := query.Offset(offset).Limit(limit).
		Order("appointment_datetime DESC").
		Find(&appointments).Error

	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"msg":   "取得預約列表失敗",
			"error": err.Error(),
		})
		return
	}

	result := map[string]interface{}{
		"appointments": appointments,
		"total":        total,
		"page":         page,
		"limit":        limit,
	}

	ctx.JSON(http.StatusOK, gin.H{
		"msg":  "取得預約列表成功",
		"data": result,
	})
}

// ConfirmAppointment 確認預約
func (c *AdminConsultationV2Controller) ConfirmAppointment(ctx *gin.Context) {
	appointmentIDStr := ctx.Param("id")
	appointmentID, err := strconv.ParseUint(appointmentIDStr, 10, 32)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"msg":   "無效的預約ID",
			"error": err.Error(),
		})
		return
	}

	// 取得管理員ID
	adminID, exists := ctx.Get("admin_id")
	if !exists {
		ctx.JSON(http.StatusUnauthorized, gin.H{
			"msg": "未登入",
		})
		return
	}

	err = c.service.ConfirmAppointment(uint(appointmentID), adminID.(uint))
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"msg":   "確認預約失敗",
			"error": err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"msg": "確認預約成功",
	})
}

// CancelAppointment 取消預約
func (c *AdminConsultationV2Controller) CancelAppointment(ctx *gin.Context) {
	appointmentIDStr := ctx.Param("id")
	appointmentID, err := strconv.ParseUint(appointmentIDStr, 10, 32)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"msg":   "無效的預約ID",
			"error": err.Error(),
		})
		return
	}

	// 取得管理員ID
	adminID, exists := ctx.Get("admin_id")
	if !exists {
		ctx.JSON(http.StatusUnauthorized, gin.H{
			"msg": "未登入",
		})
		return
	}

	err = c.service.CancelAppointment(uint(appointmentID), adminID.(uint), true)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"msg":   "取消預約失敗",
			"error": err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"msg": "取消預約成功",
	})
}

// GetDashboard 取得總覽資料
func (c *AdminConsultationV2Controller) GetDashboard(ctx *gin.Context) {
	// 統計資料
	var totalModules int64
	c.db.Model(&ConsultationModule{}).Count(&totalModules)

	var pendingAppointments int64
	c.db.Model(&ConsultationAppointment{}).Where("status = ?", "pending").Count(&pendingAppointments)

	today := time.Now().Format("2006-01-02")
	var todayAppointments int64
	c.db.Model(&ConsultationAppointment{}).Where("DATE(appointment_datetime) = ?", today).Count(&todayAppointments)

	var pendingSubmissions int64
	c.db.Model(&MemberAssignmentSubmission{}).Where("status = ?", "pending").Count(&pendingSubmissions)

	// 最近活動
	var recentAppointments []ConsultationAppointment
	c.db.Preload("Member").Preload("Module").
		Order("created_at DESC").Limit(5).Find(&recentAppointments)

	var recentSubmissions []MemberAssignmentSubmission
	c.db.Preload("Member").Preload("Assignment").
		Order("submitted_at DESC").Limit(5).Find(&recentSubmissions)

	// 組合活動資料
	recentActivities := []map[string]interface{}{}

	for _, appointment := range recentAppointments {
		recentActivities = append(recentActivities, map[string]interface{}{
			"type":        "appointment",
			"title":       "新預約申請",
			"description": appointment.Member.Name + " 預約了 " + appointment.Module.Name,
			"created_at":  appointment.CreatedAt,
		})
	}

	for _, submission := range recentSubmissions {
		recentActivities = append(recentActivities, map[string]interface{}{
			"type":        "submission",
			"title":       "作業提交",
			"description": submission.Member.Name + " 提交了作業",
			"created_at":  submission.SubmittedAt,
		})
	}

	// 系統警告
	systemAlerts := []map[string]interface{}{}

	// 檢查是否有過期的待確認預約
	var expiredAppointments int64
	expiredTime := time.Now().Add(-24 * time.Hour)
	c.db.Model(&ConsultationAppointment{}).
		Where("status = ? AND created_at < ?", "pending", expiredTime).
		Count(&expiredAppointments)

	if expiredAppointments > 0 {
		systemAlerts = append(systemAlerts, map[string]interface{}{
			"title":      "預約處理延遲",
			"message":    fmt.Sprintf("有 %d 個預約申請超過 24 小時未處理", expiredAppointments),
			"created_at": time.Now(),
		})
	}

	// 圖表資料
	chartData := map[string]interface{}{
		"appointment_trend":   c.getAppointmentTrendData(),
		"module_distribution": c.getModuleDistributionData(),
	}

	ctx.JSON(http.StatusOK, gin.H{
		"msg": "取得總覽資料成功",
		"data": map[string]interface{}{
			"statistics": map[string]interface{}{
				"total_modules":        totalModules,
				"pending_appointments": pendingAppointments,
				"today_appointments":   todayAppointments,
				"pending_submissions":  pendingSubmissions,
			},
			"recent_activities": recentActivities,
			"system_alerts":     systemAlerts,
			"charts":            chartData,
		},
	})
}

// 取得預約趨勢資料
func (c *AdminConsultationV2Controller) getAppointmentTrendData() map[string]interface{} {
	// 取得過去7天的預約數據
	labels := []string{}
	data := []int{}

	for i := 6; i >= 0; i-- {
		date := time.Now().AddDate(0, 0, -i)
		dateStr := date.Format("2006-01-02")
		labels = append(labels, date.Format("01/02"))

		var count int64
		c.db.Model(&ConsultationAppointment{}).
			Where("DATE(appointment_datetime) = ?", dateStr).
			Count(&count)
		data = append(data, int(count))
	}

	return map[string]interface{}{
		"labels": labels,
		"data":   data,
	}
}

// 取得模組分布資料
func (c *AdminConsultationV2Controller) getModuleDistributionData() map[string]interface{} {
	var results []struct {
		ModuleName string
		Count      int64
	}

	c.db.Model(&ConsultationAppointment{}).
		Select("consultation_modules.name as module_name, COUNT(*) as count").
		Joins("JOIN consultation_modules ON consultation_modules.id = consultation_appointments.module_id").
		Group("consultation_modules.id, consultation_modules.name").
		Scan(&results)

	labels := []string{}
	data := []int64{}

	for _, result := range results {
		labels = append(labels, result.ModuleName)
		data = append(data, result.Count)
	}

	return map[string]interface{}{
		"labels": labels,
		"data":   data,
	}
}
