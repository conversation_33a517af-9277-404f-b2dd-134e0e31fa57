//go:build ignore
// +build ignore

package main

import (
	"fmt"
	"log"
	"os"
	"time"

	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"

	. "cx/app/models"
)

// PerformanceOptimizer 效能優化器
type PerformanceOptimizer struct {
	db *gorm.DB
}

// NewPerformanceOptimizer 建立效能優化器
func NewPerformanceOptimizer() *PerformanceOptimizer {
	// 連接資料庫
	dsn := "root:forwork0926@tcp(localhost:3306)/grace?parseTime=True&loc=Local&charset=utf8mb4&collation=utf8mb4_unicode_ci"
	db, err := gorm.Open(mysql.Open(dsn), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Info),
	})
	if err != nil {
		log.Fatal("連接資料庫失敗:", err)
	}

	return &PerformanceOptimizer{db: db}
}

// OptimizeDatabase 優化資料庫
func (p *PerformanceOptimizer) OptimizeDatabase() error {
	fmt.Println("🚀 開始資料庫效能優化...")

	// 1. 建立索引
	if err := p.createIndexes(); err != nil {
		return fmt.Errorf("建立索引失敗: %w", err)
	}

	// 2. 分析表格
	if err := p.analyzeTables(); err != nil {
		return fmt.Errorf("分析表格失敗: %w", err)
	}

	// 3. 優化查詢
	if err := p.optimizeQueries(); err != nil {
		return fmt.Errorf("優化查詢失敗: %w", err)
	}

	// 4. 清理無用資料
	if err := p.cleanupData(); err != nil {
		return fmt.Errorf("清理資料失敗: %w", err)
	}

	fmt.Println("✅ 資料庫效能優化完成！")
	return nil
}

// createIndexes 建立索引
func (p *PerformanceOptimizer) createIndexes() error {
	fmt.Println("📊 建立資料庫索引...")

	indexes := []struct {
		table   string
		columns []string
		name    string
	}{
		// 諮詢模組索引
		{"consultation_modules", []string{"category", "is_active"}, "idx_modules_category_active"},
		{"consultation_modules", []string{"sort_order"}, "idx_modules_sort_order"},
		{"consultation_modules", []string{"created_at"}, "idx_modules_created_at"},

		// 作業索引
		{"consultation_assignments", []string{"module_id", "sort_order"}, "idx_assignments_module_sort"},
		{"consultation_assignments", []string{"created_at"}, "idx_assignments_created_at"},

		// 作業提交索引
		{"member_assignment_submissions", []string{"assignment_id", "member_id"}, "idx_submissions_assignment_member"},
		{"member_assignment_submissions", []string{"member_id", "status"}, "idx_submissions_member_status"},
		{"member_assignment_submissions", []string{"status", "submitted_at"}, "idx_submissions_status_submitted"},
		{"member_assignment_submissions", []string{"reviewed_at"}, "idx_submissions_reviewed_at"},

		// 預約索引
		{"consultation_appointments", []string{"member_id", "status"}, "idx_appointments_member_status"},
		{"consultation_appointments", []string{"module_id", "appointment_datetime"}, "idx_appointments_module_datetime"},
		{"consultation_appointments", []string{"appointment_datetime", "status"}, "idx_appointments_datetime_status"},
		{"consultation_appointments", []string{"created_at"}, "idx_appointments_created_at"},
		{"consultation_appointments", []string{"confirmed_at"}, "idx_appointments_confirmed_at"},

		// 時段索引
		{"consultation_time_slots", []string{"date", "is_available"}, "idx_time_slots_date_available"},
		{"consultation_time_slots", []string{"date", "start_time", "end_time"}, "idx_time_slots_datetime"},

		// 資格規則索引
		{"consultation_eligibility_rules", []string{"module_id", "is_active"}, "idx_eligibility_module_active"},

		// 檔案上傳索引
		{"file_uploads", []string{"member_id", "category"}, "idx_files_member_category"},
		{"file_uploads", []string{"md5_hash"}, "idx_files_md5"},
		{"file_uploads", []string{"created_at"}, "idx_files_created_at"},

		// 會員索引（如果需要）
		{"members", []string{"status", "level"}, "idx_members_status_level"},
		{"members", []string{"created_at"}, "idx_members_created_at"},
	}

	for _, idx := range indexes {
		sql := fmt.Sprintf("CREATE INDEX IF NOT EXISTS %s ON %s (%s)",
			idx.name, idx.table, joinColumns(idx.columns))

		if err := p.db.Exec(sql).Error; err != nil {
			fmt.Printf("⚠️  建立索引 %s 失敗: %v\n", idx.name, err)
		} else {
			fmt.Printf("✅ 建立索引: %s\n", idx.name)
		}
	}

	return nil
}

// analyzeTables 分析表格
func (p *PerformanceOptimizer) analyzeTables() error {
	fmt.Println("🔍 分析表格統計資訊...")

	tables := []string{
		"consultation_modules",
		"consultation_assignments",
		"member_assignment_submissions",
		"consultation_appointments",
		"consultation_time_slots",
		"consultation_eligibility_rules",
		"file_uploads",
		"members",
	}

	for _, table := range tables {
		sql := fmt.Sprintf("ANALYZE TABLE %s", table)
		if err := p.db.Exec(sql).Error; err != nil {
			fmt.Printf("⚠️  分析表格 %s 失敗: %v\n", table, err)
		} else {
			fmt.Printf("✅ 分析表格: %s\n", table)
		}
	}

	return nil
}

// optimizeQueries 優化查詢
func (p *PerformanceOptimizer) optimizeQueries() error {
	fmt.Println("⚡ 優化常用查詢...")

	// 測試常用查詢的效能
	queries := []struct {
		name  string
		query func() error
	}{
		{
			"取得活躍模組列表",
			func() error {
				var modules []ConsultationModule
				start := time.Now()
				err := p.db.Where("is_active = ?", true).Order("sort_order ASC").Find(&modules).Error
				duration := time.Since(start)
				fmt.Printf("  查詢時間: %v\n", duration)
				return err
			},
		},
		{
			"取得會員預約列表",
			func() error {
				var appointments []ConsultationAppointment
				start := time.Now()
				err := p.db.Preload("Module").Where("member_id = ?", 1).
					Order("appointment_datetime DESC").Limit(10).Find(&appointments).Error
				duration := time.Since(start)
				fmt.Printf("  查詢時間: %v\n", duration)
				return err
			},
		},
		{
			"取得待審核作業",
			func() error {
				var submissions []MemberAssignmentSubmission
				start := time.Now()
				err := p.db.Preload("Assignment").Preload("Member").
					Where("status = ?", "pending").
					Order("submitted_at ASC").Limit(20).Find(&submissions).Error
				duration := time.Since(start)
				fmt.Printf("  查詢時間: %v\n", duration)
				return err
			},
		},
		{
			"取得可用時段",
			func() error {
				var timeSlots []ConsultationTimeSlot
				start := time.Now()
				today := time.Now().Format("2006-01-02")
				err := p.db.Where("date >= ? AND is_available = ?", today, true).
					Order("date ASC, start_time ASC").Limit(50).Find(&timeSlots).Error
				duration := time.Since(start)
				fmt.Printf("  查詢時間: %v\n", duration)
				return err
			},
		},
	}

	for _, q := range queries {
		fmt.Printf("🔍 測試查詢: %s\n", q.name)
		if err := q.query(); err != nil {
			fmt.Printf("⚠️  查詢失敗: %v\n", err)
		}
	}

	return nil
}

// cleanupData 清理無用資料
func (p *PerformanceOptimizer) cleanupData() error {
	fmt.Println("🧹 清理無用資料...")

	// 清理過期的時段（超過30天的過去時段）
	cutoffDate := time.Now().AddDate(0, 0, -30).Format("2006-01-02")
	result := p.db.Where("date < ? AND is_available = ?", cutoffDate, false).
		Delete(&ConsultationTimeSlot{})
	if result.Error != nil {
		fmt.Printf("⚠️  清理過期時段失敗: %v\n", result.Error)
	} else {
		fmt.Printf("✅ 清理過期時段: %d 筆\n", result.RowsAffected)
	}

	// 清理孤立的檔案記錄（沒有關聯的檔案）
	var orphanFiles []FileUpload
	p.db.Where("id NOT IN (SELECT DISTINCT file_id FROM member_assignment_submission_files WHERE file_id IS NOT NULL)").
		Find(&orphanFiles)

	for _, file := range orphanFiles {
		// 檢查檔案是否實際存在
		if _, err := os.Stat(file.FilePath); os.IsNotExist(err) {
			// 檔案不存在，刪除記錄
			p.db.Delete(&file)
			fmt.Printf("✅ 清理孤立檔案記錄: %s\n", file.OriginalName)
		}
	}

	// 清理過期的會話資料（如果有的話）
	// 這裡可以添加其他清理邏輯

	return nil
}

// generatePerformanceReport 生成效能報告
func (p *PerformanceOptimizer) generatePerformanceReport() error {
	fmt.Println("📊 生成效能報告...")

	report := `
# 諮詢系統效能報告
生成時間: %s

## 資料庫統計

### 表格大小
`

	// 取得表格大小資訊
	var tableStats []struct {
		TableName string `gorm:"column:TABLE_NAME"`
		DataSize  int64  `gorm:"column:DATA_LENGTH"`
		IndexSize int64  `gorm:"column:INDEX_LENGTH"`
		Rows      int64  `gorm:"column:TABLE_ROWS"`
	}

	p.db.Raw(`
		SELECT TABLE_NAME, DATA_LENGTH, INDEX_LENGTH, TABLE_ROWS
		FROM information_schema.TABLES 
		WHERE TABLE_SCHEMA = DATABASE() 
		AND TABLE_NAME LIKE 'consultation_%' OR TABLE_NAME = 'file_uploads'
		ORDER BY DATA_LENGTH DESC
	`).Scan(&tableStats)

	report += "| 表格名稱 | 資料大小 | 索引大小 | 記錄數 |\n"
	report += "|---------|---------|---------|-------|\n"

	for _, stat := range tableStats {
		report += fmt.Sprintf("| %s | %s | %s | %d |\n",
			stat.TableName,
			formatBytes(stat.DataSize),
			formatBytes(stat.IndexSize),
			stat.Rows)
	}

	// 寫入報告檔案
	reportFile := fmt.Sprintf("performance_report_%s.md", time.Now().Format("20060102_150405"))
	if err := os.WriteFile(reportFile, []byte(fmt.Sprintf(report, time.Now().Format("2006-01-02 15:04:05"))), 0644); err != nil {
		return fmt.Errorf("寫入報告失敗: %w", err)
	}

	fmt.Printf("✅ 效能報告已生成: %s\n", reportFile)
	return nil
}

// 輔助函數
func joinColumns(columns []string) string {
	result := ""
	for i, col := range columns {
		if i > 0 {
			result += ", "
		}
		result += col
	}
	return result
}

func formatBytes(bytes int64) string {
	const unit = 1024
	if bytes < unit {
		return fmt.Sprintf("%d B", bytes)
	}
	div, exp := int64(unit), 0
	for n := bytes / unit; n >= unit; n /= unit {
		div *= unit
		exp++
	}
	return fmt.Sprintf("%.1f %cB", float64(bytes)/float64(div), "KMGTPE"[exp])
}

func main() {
	fmt.Println("🚀 諮詢系統效能優化工具")
	fmt.Println("================================")

	optimizer := NewPerformanceOptimizer()

	// 執行優化
	if err := optimizer.OptimizeDatabase(); err != nil {
		log.Fatal("優化失敗:", err)
	}

	// 生成報告
	if err := optimizer.generatePerformanceReport(); err != nil {
		log.Printf("生成報告失敗: %v", err)
	}

	fmt.Println("\n🎉 效能優化完成！")
	fmt.Println("\n建議:")
	fmt.Println("1. 定期執行此優化腳本（建議每週一次）")
	fmt.Println("2. 監控查詢效能，特別是慢查詢")
	fmt.Println("3. 根據使用情況調整索引策略")
	fmt.Println("4. 定期清理無用資料")
	fmt.Println("5. 考慮實施資料庫分區（如果資料量很大）")
}
