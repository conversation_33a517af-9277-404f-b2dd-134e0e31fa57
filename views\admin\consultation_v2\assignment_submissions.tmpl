{{define "admin/consultation_v2/assignment_submissions"}}
<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>作業審核 - G.R.A.C.E 優雅學院後台</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .submission-card {
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            background: #fff;
            transition: all 0.2s ease;
        }
        .submission-card:hover {
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .status-badge {
            padding: 5px 10px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
        }
        .status-pending { background: #fff3cd; color: #856404; }
        .status-approved { background: #d4edda; color: #155724; }
        .status-rejected { background: #f8d7da; color: #721c24; }
        .priority-high { border-left: 4px solid #dc3545; }
        .priority-normal { border-left: 4px solid #28a745; }
        .filter-section {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .member-info {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 10px;
        }
        .file-list {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            margin-top: 10px;
        }
        .file-item {
            display: flex;
            align-items: center;
            padding: 5px 0;
        }
        .file-item i {
            margin-right: 8px;
            color: #007bff;
        }
        .review-form {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin-top: 15px;
        }
    </style>
</head>
<body>
    <div class="container-fluid mt-4">
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2><i class="fas fa-tasks"></i> 作業審核</h2>
                    <div>
                        <button class="btn btn-primary" onclick="exportSubmissions()">
                            <i class="fas fa-download"></i> 匯出資料
                        </button>
                        <a href="/admin/consultations/v2" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left"></i> 返回總覽
                        </a>
                    </div>
                </div>

                <!-- 統計卡片 -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="stats-card">
                            <h5><i class="fas fa-clock"></i> 待審核</h5>
                            <h2 id="pendingCount">0</h2>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stats-card">
                            <h5><i class="fas fa-check-circle"></i> 已通過</h5>
                            <h2 id="approvedCount">0</h2>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stats-card">
                            <h5><i class="fas fa-times-circle"></i> 需修改</h5>
                            <h2 id="rejectedCount">0</h2>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stats-card">
                            <h5><i class="fas fa-calendar-day"></i> 今日提交</h5>
                            <h2 id="todayCount">0</h2>
                        </div>
                    </div>
                </div>

                <!-- 篩選區域 -->
                <div class="filter-section">
                    <div class="row">
                        <div class="col-md-2">
                            <label class="form-label">狀態篩選</label>
                            <select class="form-select" id="statusFilter" onchange="filterSubmissions()">
                                <option value="">全部狀態</option>
                                <option value="pending">待審核</option>
                                <option value="approved">已通過</option>
                                <option value="rejected">需修改</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">模組篩選</label>
                            <select class="form-select" id="moduleFilter" onchange="filterSubmissions()">
                                <option value="">全部模組</option>
                                <!-- 模組選項將由 JavaScript 動態載入 -->
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">日期範圍</label>
                            <select class="form-select" id="dateFilter" onchange="filterSubmissions()">
                                <option value="">全部日期</option>
                                <option value="today">今天</option>
                                <option value="yesterday">昨天</option>
                                <option value="week">本週</option>
                                <option value="month">本月</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">搜尋</label>
                            <input type="text" class="form-control" id="searchInput" placeholder="搜尋會員姓名或作業標題..." onkeyup="filterSubmissions()">
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">排序</label>
                            <select class="form-select" id="sortOrder" onchange="filterSubmissions()">
                                <option value="submitted_desc">提交時間 (新到舊)</option>
                                <option value="submitted_asc">提交時間 (舊到新)</option>
                                <option value="status">狀態</option>
                                <option value="member">會員姓名</option>
                            </select>
                        </div>
                    </div>
                </div>

                <!-- 作業提交列表 -->
                <div id="submissionsList">
                    <!-- 提交項目將由 JavaScript 動態載入 -->
                </div>

                <!-- 分頁 -->
                <nav aria-label="作業分頁" id="pagination" style="display: none;">
                    <ul class="pagination justify-content-center">
                        <!-- 分頁將由 JavaScript 生成 -->
                    </ul>
                </nav>
            </div>
        </div>
    </div>

    <!-- 審核模態框 -->
    <div class="modal fade" id="reviewModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">作業審核</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="submissionDetails">
                    <!-- 詳情內容將由 JavaScript 動態載入 -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">關閉</button>
                    <div id="reviewActions">
                        <!-- 審核按鈕將由 JavaScript 動態載入 -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 審核確認模態框 -->
    <div class="modal fade" id="confirmReviewModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="confirmReviewTitle">確認審核</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p id="confirmReviewMessage"></p>
                    <div class="mb-3">
                        <label for="reviewNote" class="form-label">審核意見 *</label>
                        <textarea class="form-control" id="reviewNote" rows="4" required 
                                  placeholder="請提供詳細的審核意見..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="confirmReviewBtn">確認</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let submissions = [];
        let filteredSubmissions = [];
        let modules = [];
        let currentPage = 1;
        let totalPages = 1;
        let reviewModal, confirmReviewModal;
        let currentReviewAction = null;

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            reviewModal = new bootstrap.Modal(document.getElementById('reviewModal'));
            confirmReviewModal = new bootstrap.Modal(document.getElementById('confirmReviewModal'));
            
            loadSubmissions();
            loadModules();
            loadStatistics();
        });

        // 載入作業提交列表
        function loadSubmissions() {
            const params = new URLSearchParams({
                page: currentPage,
                limit: 20
            });

            fetch(`/api/admin/v2/submissions?${params}`)
            .then(response => response.json())
            .then(data => {
                if (data.msg === '取得作業提交列表成功') {
                    submissions = data.data.submissions;
                    filteredSubmissions = [...submissions];
                    totalPages = Math.ceil(data.data.total / 20);
                    renderSubmissions();
                    updateStatistics();
                } else {
                    console.error('載入作業提交失敗:', data.error);
                }
            })
            .catch(error => {
                console.error('載入作業提交失敗:', error);
            });
        }

        // 載入模組列表
        function loadModules() {
            fetch('/api/admin/v2/modules')
            .then(response => response.json())
            .then(data => {
                if (data.msg === '取得模組列表成功') {
                    modules = data.data;
                    renderModuleFilter();
                }
            });
        }

        // 渲染模組篩選選項
        function renderModuleFilter() {
            const moduleFilter = document.getElementById('moduleFilter');
            moduleFilter.innerHTML = '<option value="">全部模組</option>';
            
            modules.forEach(module => {
                const option = document.createElement('option');
                option.value = module.id;
                option.textContent = module.name;
                moduleFilter.appendChild(option);
            });
        }

        // 載入統計資料
        function loadStatistics() {
            fetch('/api/admin/v2/submissions/statistics')
            .then(response => response.json())
            .then(data => {
                if (data.msg === '取得統計資料成功') {
                    const stats = data.data;
                    document.getElementById('pendingCount').textContent = stats.pending_count || 0;
                    document.getElementById('approvedCount').textContent = stats.approved_count || 0;
                    document.getElementById('rejectedCount').textContent = stats.rejected_count || 0;
                    document.getElementById('todayCount').textContent = stats.today_count || 0;
                }
            });
        }

        // 渲染作業提交列表
        function renderSubmissions() {
            const container = document.getElementById('submissionsList');
            
            if (filteredSubmissions.length === 0) {
                container.innerHTML = `
                    <div class="text-center py-5">
                        <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                        <h5>暫無作業提交</h5>
                        <p class="text-muted">目前沒有符合篩選條件的作業提交記錄</p>
                    </div>
                `;
                return;
            }

            container.innerHTML = filteredSubmissions.map(submission => {
                const isUrgent = isSubmissionUrgent(submission);
                return `
                    <div class="submission-card ${isUrgent ? 'priority-high' : 'priority-normal'}">
                        <div class="d-flex justify-content-between align-items-start mb-3">
                            <div>
                                <h5>${submission.assignment.title}</h5>
                                <div class="member-info">
                                    <strong>${submission.member.name}</strong> (${submission.member.uid})
                                    <br><small>會員等級: ${getMemberLevelText(submission.member.level)}</small>
                                </div>
                            </div>
                            <div class="text-end">
                                <span class="status-badge status-${submission.status}">
                                    ${getStatusText(submission.status)}
                                </span>
                                ${isUrgent ? '<div class="text-danger small mt-1"><i class="fas fa-exclamation-triangle"></i> 緊急</div>' : ''}
                            </div>
                        </div>
                        
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <p><i class="fas fa-puzzle-piece"></i> <strong>模組:</strong> ${submission.assignment.module.name}</p>
                                <p><i class="fas fa-calendar"></i> <strong>提交時間:</strong> ${formatDateTime(submission.submitted_at)}</p>
                                ${submission.reviewed_at ? `<p><i class="fas fa-check"></i> <strong>審核時間:</strong> ${formatDateTime(submission.reviewed_at)}</p>` : ''}
                            </div>
                            <div class="col-md-6">
                                ${submission.file_paths && submission.file_paths.length > 0 ? `
                                    <div class="file-list">
                                        <strong><i class="fas fa-paperclip"></i> 附件檔案:</strong>
                                        ${submission.file_paths.map(file => `
                                            <div class="file-item">
                                                <i class="fas fa-file"></i>
                                                <a href="/api/files/${file.id}/download" target="_blank">${file.original_name}</a>
                                            </div>
                                        `).join('')}
                                    </div>
                                ` : '<p class="text-muted">無附件檔案</p>'}
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <strong>作業內容:</strong>
                            <p class="mb-2">${submission.content}</p>
                            ${submission.review_note ? `<div class="alert alert-info small">審核意見: ${submission.review_note}</div>` : ''}
                        </div>
                        
                        <div class="d-flex justify-content-between align-items-center">
                            <button class="btn btn-outline-info btn-sm" onclick="viewSubmissionDetails(${submission.id})">
                                <i class="fas fa-eye"></i> 查看詳情
                            </button>
                            <div>
                                ${getActionButtons(submission)}
                            </div>
                        </div>
                    </div>
                `;
            }).join('');
        }

        // 檢查作業是否緊急
        function isSubmissionUrgent(submission) {
            if (submission.status !== 'pending') return false;
            
            const submittedTime = new Date(submission.submitted_at);
            const now = new Date();
            const hoursDiff = (now - submittedTime) / (1000 * 60 * 60);
            
            return hoursDiff >= 48; // 48小時未審核的作業
        }

        // 取得狀態文字
        function getStatusText(status) {
            const statusMap = {
                'pending': '待審核',
                'approved': '已通過',
                'rejected': '需修改'
            };
            return statusMap[status] || status;
        }

        // 取得會員等級文字
        function getMemberLevelText(level) {
            const levelMap = {
                1: '一般會員',
                2: '青銅會員',
                3: '鑽石會員',
                4: '至尊會員',
                5: '皇家至尊'
            };
            return levelMap[level] || '未知等級';
        }

        // 格式化日期時間
        function formatDateTime(dateTimeStr) {
            const date = new Date(dateTimeStr);
            return date.toLocaleString('zh-TW', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit'
            });
        }

        // 取得操作按鈕
        function getActionButtons(submission) {
            let buttons = [];
            
            if (submission.status === 'pending') {
                buttons.push(`
                    <button class="btn btn-success btn-sm me-2" onclick="reviewSubmission(${submission.id}, 'approved')">
                        <i class="fas fa-check"></i> 通過
                    </button>
                    <button class="btn btn-warning btn-sm" onclick="reviewSubmission(${submission.id}, 'rejected')">
                        <i class="fas fa-edit"></i> 需修改
                    </button>
                `);
            } else if (submission.status === 'rejected') {
                buttons.push(`
                    <button class="btn btn-success btn-sm" onclick="reviewSubmission(${submission.id}, 'approved')">
                        <i class="fas fa-check"></i> 通過
                    </button>
                `);
            }
            
            return buttons.join('');
        }

        // 篩選作業提交
        function filterSubmissions() {
            const statusFilter = document.getElementById('statusFilter').value;
            const moduleFilter = document.getElementById('moduleFilter').value;
            const dateFilter = document.getElementById('dateFilter').value;
            const searchInput = document.getElementById('searchInput').value.toLowerCase();
            const sortOrder = document.getElementById('sortOrder').value;

            filteredSubmissions = submissions.filter(submission => {
                const matchStatus = !statusFilter || submission.status === statusFilter;
                const matchModule = !moduleFilter || submission.assignment.module_id.toString() === moduleFilter;
                const matchDate = !dateFilter || matchDateFilter(submission, dateFilter);
                const matchSearch = !searchInput || 
                    submission.member.name.toLowerCase().includes(searchInput) ||
                    submission.assignment.title.toLowerCase().includes(searchInput) ||
                    submission.content.toLowerCase().includes(searchInput);

                return matchStatus && matchModule && matchDate && matchSearch;
            });

            // 排序
            filteredSubmissions.sort((a, b) => {
                switch (sortOrder) {
                    case 'submitted_asc':
                        return new Date(a.submitted_at) - new Date(b.submitted_at);
                    case 'submitted_desc':
                        return new Date(b.submitted_at) - new Date(a.submitted_at);
                    case 'status':
                        return a.status.localeCompare(b.status);
                    case 'member':
                        return a.member.name.localeCompare(b.member.name);
                    default:
                        return new Date(b.submitted_at) - new Date(a.submitted_at);
                }
            });

            renderSubmissions();
        }

        // 日期篩選匹配
        function matchDateFilter(submission, filter) {
            const submissionDate = new Date(submission.submitted_at);
            const now = new Date();
            
            switch (filter) {
                case 'today':
                    return submissionDate.toDateString() === now.toDateString();
                case 'yesterday':
                    const yesterday = new Date(now);
                    yesterday.setDate(yesterday.getDate() - 1);
                    return submissionDate.toDateString() === yesterday.toDateString();
                case 'week':
                    const weekStart = new Date(now);
                    weekStart.setDate(now.getDate() - now.getDay());
                    const weekEnd = new Date(weekStart);
                    weekEnd.setDate(weekStart.getDate() + 6);
                    return submissionDate >= weekStart && submissionDate <= weekEnd;
                case 'month':
                    return submissionDate.getMonth() === now.getMonth() && 
                           submissionDate.getFullYear() === now.getFullYear();
                default:
                    return true;
            }
        }

        // 查看作業詳情
        function viewSubmissionDetails(submissionId) {
            const submission = submissions.find(s => s.id === submissionId);
            if (!submission) return;

            const detailsHtml = `
                <div class="row">
                    <div class="col-md-6">
                        <h6>基本資訊</h6>
                        <table class="table table-sm">
                            <tr><td>作業標題</td><td>${submission.assignment.title}</td></tr>
                            <tr><td>所屬模組</td><td>${submission.assignment.module.name}</td></tr>
                            <tr><td>提交時間</td><td>${formatDateTime(submission.submitted_at)}</td></tr>
                            <tr><td>狀態</td><td><span class="status-badge status-${submission.status}">${getStatusText(submission.status)}</span></td></tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <h6>會員資訊</h6>
                        <table class="table table-sm">
                            <tr><td>姓名</td><td>${submission.member.name}</td></tr>
                            <tr><td>帳號</td><td>${submission.member.uid}</td></tr>
                            <tr><td>會員等級</td><td>${getMemberLevelText(submission.member.level)}</td></tr>
                        </table>
                    </div>
                </div>
                
                <h6>作業要求</h6>
                <p>${submission.assignment.description}</p>
                ${submission.assignment.requirements ? `<p><strong>提交要求:</strong> ${submission.assignment.requirements}</p>` : ''}
                
                <h6>作業內容</h6>
                <div class="border p-3 rounded bg-light">
                    ${submission.content}
                </div>
                
                ${submission.file_paths && submission.file_paths.length > 0 ? `
                    <h6 class="mt-3">附件檔案</h6>
                    <div class="file-list">
                        ${submission.file_paths.map(file => `
                            <div class="file-item">
                                <i class="fas fa-file"></i>
                                <a href="/api/files/${file.id}/download" target="_blank">${file.original_name}</a>
                                <small class="text-muted">(${formatFileSize(file.file_size)})</small>
                            </div>
                        `).join('')}
                    </div>
                ` : ''}
                
                ${submission.review_note ? `
                    <h6 class="mt-3">審核意見</h6>
                    <div class="alert alert-info">
                        ${submission.review_note}
                    </div>
                ` : ''}
                
                <h6 class="mt-3">時間記錄</h6>
                <table class="table table-sm">
                    <tr><td>提交時間</td><td>${formatDateTime(submission.submitted_at)}</td></tr>
                    ${submission.reviewed_at ? `<tr><td>審核時間</td><td>${formatDateTime(submission.reviewed_at)}</td></tr>` : ''}
                </table>
            `;

            document.getElementById('submissionDetails').innerHTML = detailsHtml;
            
            // 設置操作按鈕
            const actionsHtml = getActionButtons(submission);
            document.getElementById('reviewActions').innerHTML = actionsHtml;
            
            reviewModal.show();
        }

        // 審核作業
        function reviewSubmission(submissionId, status) {
            currentReviewAction = { id: submissionId, status: status };
            
            const statusText = status === 'approved' ? '通過' : '需修改';
            document.getElementById('confirmReviewTitle').textContent = `確認${statusText}`;
            document.getElementById('confirmReviewMessage').textContent = `確定要將此作業標記為「${statusText}」嗎？`;
            document.getElementById('reviewNote').value = '';
            document.getElementById('confirmReviewBtn').textContent = `確認${statusText}`;
            
            confirmReviewModal.show();
        }

        // 執行審核
        document.getElementById('confirmReviewBtn').addEventListener('click', function() {
            if (!currentReviewAction) return;

            const note = document.getElementById('reviewNote').value.trim();
            if (!note) {
                alert('請填寫審核意見');
                return;
            }

            const requestData = {
                status: currentReviewAction.status,
                review_note: note
            };

            fetch(`/api/admin/v2/submissions/${currentReviewAction.id}/review`, {
                method: 'PATCH',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(requestData)
            })
            .then(response => response.json())
            .then(data => {
                if (data.msg === '審核作業成功') {
                    confirmReviewModal.hide();
                    reviewModal.hide();
                    loadSubmissions();
                    alert('審核完成！');
                } else {
                    alert('審核失敗: ' + data.error);
                }
            })
            .catch(error => {
                alert('審核失敗: ' + error.message);
            });
        });

        // 匯出作業資料
        function exportSubmissions() {
            const params = new URLSearchParams();
            
            // 添加當前篩選條件
            const statusFilter = document.getElementById('statusFilter').value;
            const moduleFilter = document.getElementById('moduleFilter').value;
            const dateFilter = document.getElementById('dateFilter').value;
            
            if (statusFilter) params.append('status', statusFilter);
            if (moduleFilter) params.append('module_id', moduleFilter);
            if (dateFilter) params.append('date_filter', dateFilter);

            window.open(`/api/admin/v2/submissions/export?${params}`, '_blank');
        }

        // 格式化檔案大小
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        // 更新統計
        function updateStatistics() {
            const pending = submissions.filter(s => s.status === 'pending').length;
            const approved = submissions.filter(s => s.status === 'approved').length;
            const rejected = submissions.filter(s => s.status === 'rejected').length;
            
            const today = new Date().toDateString();
            const todaySubmissions = submissions.filter(s => 
                new Date(s.submitted_at).toDateString() === today
            ).length;

            document.getElementById('pendingCount').textContent = pending;
            document.getElementById('approvedCount').textContent = approved;
            document.getElementById('rejectedCount').textContent = rejected;
            document.getElementById('todayCount').textContent = todaySubmissions;
        }
    </script>
</body>
</html>
{{end}}
