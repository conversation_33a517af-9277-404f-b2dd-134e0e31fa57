package test

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"sync"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/suite"
	"gopkg.in/guregu/null.v4"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"

	"cx/app/controllers"
	. "cx/app/models"
)

// PerformanceTestSuite 效能測試套件
type PerformanceTestSuite struct {
	suite.Suite
	db     *gorm.DB
	router *gin.Engine

	// 測試資料
	testModules     []ConsultationModule
	testAssignments []ConsultationAssignment
	testMembers     []Member
}

// SetupSuite 設置測試套件
func (suite *PerformanceTestSuite) SetupSuite() {
	// 連接測試資料庫
	dsn := "root:forwork0926@tcp(localhost:3306)/grace_test?parseTime=True&loc=Local&charset=utf8mb4&collation=utf8mb4_unicode_ci"
	db, err := gorm.Open(mysql.Open(dsn), &gorm.Config{})
	suite.Require().NoError(err)

	// 自動遷移
	err = db.AutoMigrate(
		&Member{},
		&ConsultationModule{},
		&ConsultationAssignment{},
		&MemberAssignmentSubmission{},
		&ConsultationAppointment{},
		&ConsultationTimeSlot{},
		&ConsultationEligibilityRule{},
		&FileUpload{},
	)
	suite.Require().NoError(err)

	suite.db = db

	// 設置路由
	gin.SetMode(gin.TestMode)
	suite.router = gin.New()

	// 簡化的中間件
	suite.router.Use(func(c *gin.Context) {
		if memberID := c.Query("member_id"); memberID != "" {
			c.Set("member_id", 1001)
		}
		c.Next()
	})

	// 設置控制器
	consultationController := controllers.NewConsultationV2Controller(db)
	adminController := controllers.NewAdminConsultationV2Controller(db)

	// API 路由
	api := suite.router.Group("/api/v2")
	{
		api.GET("/modules", consultationController.GetModules)
		api.GET("/modules/:id/assignments", consultationController.GetModuleAssignments)
		api.POST("/assignments/submit", consultationController.SubmitAssignment)
		api.POST("/appointments", consultationController.BookAppointment)
		api.GET("/appointments", consultationController.GetMyAppointments)
	}

	admin := suite.router.Group("/api/admin/v2")
	{
		admin.GET("/modules", adminController.GetModules)
		admin.GET("/submissions", adminController.GetSubmissions)
		admin.GET("/appointments", adminController.GetAppointments)
	}
}

// SetupTest 每個測試前的設置
func (suite *PerformanceTestSuite) SetupTest() {
	// 清理測試資料
	suite.cleanupTestData()

	// 建立大量測試資料
	suite.createTestData()
}

// createTestData 建立測試資料
func (suite *PerformanceTestSuite) createTestData() {
	suite.T().Log("建立效能測試資料...")

	// 建立測試會員 (100個)
	for i := 1; i <= 100; i++ {
		member := Member{
			ID:     uint(1000 + i),
			Name:   fmt.Sprintf("測試會員%d", i),
			Uid:    fmt.Sprintf("<EMAIL>", i),
			Status: "Y",
			Level:  uint((i % 5) + 1), // 等級 1-5
		}
		suite.testMembers = append(suite.testMembers, member)
	}
	suite.db.CreateInBatches(suite.testMembers, 50)

	// 建立測試模組 (20個)
	categories := []ConsultationModuleCategory{
		CategoryBasicGuidance,
		CategoryAdvancedGuidance,
		CategorySpecializedConsultation,
		CategoryGroupSession,
		CategoryActivityGift,
	}

	for i := 1; i <= 20; i++ {
		module := ConsultationModule{
			Name:        fmt.Sprintf("測試模組%d", i),
			Description: fmt.Sprintf("這是測試模組%d的描述", i),
			Category:    categories[i%len(categories)],
			IsActive:    true,
			SortOrder:   i,
			CreatedByID: 1,
		}
		suite.testModules = append(suite.testModules, module)
	}
	suite.db.CreateInBatches(suite.testModules, 10)

	// 重新載入模組以取得ID
	suite.db.Find(&suite.testModules)

	// 建立測試作業 (每個模組2個作業，共40個)
	for _, module := range suite.testModules {
		for j := 1; j <= 2; j++ {
			assignment := ConsultationAssignment{
				ModuleID:    module.ID,
				Title:       fmt.Sprintf("模組%d作業%d", module.ID, j),
				Description: fmt.Sprintf("這是模組%d的第%d個作業", module.ID, j),
				IsRequired:  true,
				SortOrder:   j,
				CreatedByID: 1,
			}
			suite.testAssignments = append(suite.testAssignments, assignment)
		}
	}
	suite.db.CreateInBatches(suite.testAssignments, 20)

	// 建立大量作業提交記錄 (500個)
	suite.db.Find(&suite.testAssignments)
	var submissions []MemberAssignmentSubmission

	for i := 0; i < 500; i++ {
		memberIndex := i % len(suite.testMembers)
		assignmentIndex := i % len(suite.testAssignments)

		submission := MemberAssignmentSubmission{
			AssignmentID: suite.testAssignments[assignmentIndex].ID,
			MemberID:     suite.testMembers[memberIndex].ID,
			Content:      fmt.Sprintf("這是第%d個作業提交", i+1),
			Status:       SubmissionStatusSubmitted,
			SubmittedAt:  null.TimeFrom(time.Now().Add(-time.Duration(i) * time.Hour)),
		}
		submissions = append(submissions, submission)
	}
	suite.db.CreateInBatches(submissions, 100)

	// 建立大量預約記錄 (200個)
	var appointments []ConsultationAppointment

	for i := 0; i < 200; i++ {
		memberIndex := i % len(suite.testMembers)
		moduleIndex := i % len(suite.testModules)

		appointment := ConsultationAppointment{
			MemberID:            suite.testMembers[memberIndex].ID,
			ModuleID:            suite.testModules[moduleIndex].ID,
			Title:               fmt.Sprintf("預約%d", i+1),
			Description:         fmt.Sprintf("這是第%d個預約", i+1),
			AppointmentDatetime: time.Now().Add(time.Duration(i) * time.Hour),
			DurationMinutes:     60,
			Status:              "pending",
		}
		appointments = append(appointments, appointment)
	}
	suite.db.CreateInBatches(appointments, 50)

	suite.T().Log("效能測試資料建立完成")
}

// TestConcurrentModuleAccess 測試並發存取模組列表
func (suite *PerformanceTestSuite) TestConcurrentModuleAccess() {
	suite.T().Log("測試並發存取模組列表...")

	concurrency := 50
	requests := 100

	var wg sync.WaitGroup
	var mu sync.Mutex
	results := make([]time.Duration, 0, requests)
	errors := 0

	// 啟動並發請求
	for i := 0; i < concurrency; i++ {
		wg.Add(1)
		go func() {
			defer wg.Done()

			for j := 0; j < requests/concurrency; j++ {
				start := time.Now()

				w := httptest.NewRecorder()
				req, _ := http.NewRequest("GET", "/api/v2/modules", nil)
				suite.router.ServeHTTP(w, req)

				duration := time.Since(start)

				mu.Lock()
				if w.Code == http.StatusOK {
					results = append(results, duration)
				} else {
					errors++
				}
				mu.Unlock()
			}
		}()
	}

	wg.Wait()

	// 分析結果
	suite.analyzePerformanceResults("模組列表存取", results, errors)

	// 效能斷言
	suite.Equal(0, errors, "不應該有錯誤")
	suite.True(len(results) > 0, "應該有成功的請求")

	// 計算平均回應時間
	var totalTime time.Duration
	for _, duration := range results {
		totalTime += duration
	}
	avgTime := totalTime / time.Duration(len(results))

	// 平均回應時間應該小於100ms
	suite.True(avgTime < 100*time.Millisecond,
		fmt.Sprintf("平均回應時間過長: %v", avgTime))
}

// TestConcurrentSubmissionCreation 測試並發作業提交
func (suite *PerformanceTestSuite) TestConcurrentSubmissionCreation() {
	suite.T().Log("測試並發作業提交...")

	concurrency := 20
	requests := 100

	var wg sync.WaitGroup
	var mu sync.Mutex
	results := make([]time.Duration, 0, requests)
	errors := 0

	for i := 0; i < concurrency; i++ {
		wg.Add(1)
		go func(workerID int) {
			defer wg.Done()

			for j := 0; j < requests/concurrency; j++ {
				memberIndex := (workerID*requests/concurrency + j) % len(suite.testMembers)
				assignmentIndex := (workerID*requests/concurrency + j) % len(suite.testAssignments)

				submissionData := map[string]interface{}{
					"assignment_id": suite.testAssignments[assignmentIndex].ID,
					"member_id":     suite.testMembers[memberIndex].ID,
					"content":       fmt.Sprintf("並發測試作業提交 %d-%d", workerID, j),
					"file_paths":    []string{},
				}

				jsonData, _ := json.Marshal(submissionData)

				start := time.Now()

				w := httptest.NewRecorder()
				req, _ := http.NewRequest("POST", "/api/v2/assignments/submit?member_id=1001",
					bytes.NewBuffer(jsonData))
				req.Header.Set("Content-Type", "application/json")
				suite.router.ServeHTTP(w, req)

				duration := time.Since(start)

				mu.Lock()
				if w.Code == http.StatusOK {
					results = append(results, duration)
				} else {
					errors++
				}
				mu.Unlock()
			}
		}(i)
	}

	wg.Wait()

	// 分析結果
	suite.analyzePerformanceResults("作業提交", results, errors)

	// 效能斷言
	suite.True(errors < requests/10, "錯誤率應該小於10%")

	if len(results) > 0 {
		var totalTime time.Duration
		for _, duration := range results {
			totalTime += duration
		}
		avgTime := totalTime / time.Duration(len(results))

		// 平均回應時間應該小於500ms
		suite.True(avgTime < 500*time.Millisecond,
			fmt.Sprintf("作業提交平均回應時間過長: %v", avgTime))
	}
}

// TestLargeDatasetQuery 測試大資料集查詢效能
func (suite *PerformanceTestSuite) TestLargeDatasetQuery() {
	suite.T().Log("測試大資料集查詢效能...")

	// 測試管理員查詢所有提交記錄
	start := time.Now()

	w := httptest.NewRecorder()
	req, _ := http.NewRequest("GET", "/api/admin/v2/submissions?limit=100", nil)
	suite.router.ServeHTTP(w, req)

	duration := time.Since(start)

	suite.Equal(http.StatusOK, w.Code)
	suite.True(duration < 200*time.Millisecond,
		fmt.Sprintf("大資料集查詢時間過長: %v", duration))

	// 測試分頁查詢
	start = time.Now()

	w = httptest.NewRecorder()
	req, _ = http.NewRequest("GET", "/api/admin/v2/submissions?page=2&limit=50", nil)
	suite.router.ServeHTTP(w, req)

	duration = time.Since(start)

	suite.Equal(http.StatusOK, w.Code)
	suite.True(duration < 150*time.Millisecond,
		fmt.Sprintf("分頁查詢時間過長: %v", duration))
}

// TestMemoryUsage 測試記憶體使用情況
func (suite *PerformanceTestSuite) TestMemoryUsage() {
	suite.T().Log("測試記憶體使用情況...")

	// 執行大量請求並監控記憶體
	requests := 1000

	for i := 0; i < requests; i++ {
		w := httptest.NewRecorder()
		req, _ := http.NewRequest("GET", "/api/v2/modules", nil)
		suite.router.ServeHTTP(w, req)

		suite.Equal(http.StatusOK, w.Code)

		// 每100個請求檢查一次
		if i%100 == 0 {
			// 這裡可以添加記憶體使用檢查
			// runtime.GC() // 強制垃圾回收
		}
	}

	suite.T().Log("記憶體測試完成")
}

// analyzePerformanceResults 分析效能測試結果
func (suite *PerformanceTestSuite) analyzePerformanceResults(testName string, results []time.Duration, errors int) {
	if len(results) == 0 {
		suite.T().Logf("%s: 沒有成功的請求", testName)
		return
	}

	// 計算統計資料
	var totalTime time.Duration
	minTime := results[0]
	maxTime := results[0]

	for _, duration := range results {
		totalTime += duration
		if duration < minTime {
			minTime = duration
		}
		if duration > maxTime {
			maxTime = duration
		}
	}

	avgTime := totalTime / time.Duration(len(results))
	successRate := float64(len(results)) / float64(len(results)+errors) * 100

	// 計算百分位數
	// 簡化版本，實際應該排序後計算
	p95Time := maxTime // 簡化為最大值

	suite.T().Logf("=== %s 效能測試結果 ===", testName)
	suite.T().Logf("總請求數: %d", len(results)+errors)
	suite.T().Logf("成功請求: %d", len(results))
	suite.T().Logf("失敗請求: %d", errors)
	suite.T().Logf("成功率: %.2f%%", successRate)
	suite.T().Logf("平均回應時間: %v", avgTime)
	suite.T().Logf("最小回應時間: %v", minTime)
	suite.T().Logf("最大回應時間: %v", maxTime)
	suite.T().Logf("P95 回應時間: %v", p95Time)
	suite.T().Logf("每秒請求數: %.2f", float64(len(results))/totalTime.Seconds())
}

// cleanupTestData 清理測試資料
func (suite *PerformanceTestSuite) cleanupTestData() {
	tables := []string{
		"consultation_appointments",
		"member_assignment_submissions",
		"consultation_assignments",
		"consultation_modules",
		"file_uploads",
	}

	for _, table := range tables {
		suite.db.Exec(fmt.Sprintf("DELETE FROM %s", table))
	}

	suite.db.Exec("DELETE FROM members WHERE id > 1000")

	// 重置切片
	suite.testModules = nil
	suite.testAssignments = nil
	suite.testMembers = nil
}

// TearDownTest 每個測試後的清理
func (suite *PerformanceTestSuite) TearDownTest() {
	suite.cleanupTestData()
}

// TearDownSuite 測試套件結束後的清理
func (suite *PerformanceTestSuite) TearDownSuite() {
	if suite.db != nil {
		sqlDB, _ := suite.db.DB()
		sqlDB.Close()
	}
}

// TestPerformanceTestSuite 執行效能測試套件
func TestPerformanceTestSuite(t *testing.T) {
	suite.Run(t, new(PerformanceTestSuite))
}

// BenchmarkModuleListAPI 基準測試：模組列表 API
func BenchmarkModuleListAPI(b *testing.B) {
	// 設置測試環境
	gin.SetMode(gin.TestMode)
	router := gin.New()

	dsn := "root:forwork0926@tcp(localhost:3306)/grace_test?parseTime=True&loc=Local&charset=utf8mb4&collation=utf8mb4_unicode_ci"
	db, err := gorm.Open(mysql.Open(dsn), &gorm.Config{})
	if err != nil {
		b.Fatal(err)
	}

	controller := controllers.NewConsultationV2Controller(db)
	router.GET("/api/v2/modules", controller.GetModules)

	b.ResetTimer()

	for i := 0; i < b.N; i++ {
		w := httptest.NewRecorder()
		req, _ := http.NewRequest("GET", "/api/v2/modules", nil)
		router.ServeHTTP(w, req)
	}
}
