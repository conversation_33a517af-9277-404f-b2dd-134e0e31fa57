以下為 **諮詢預約系統重構說明** 的軟體工程需求書：

---

# 1. 專案背景與目的

* **背景**
  目前諮詢預約功能散落於課程與道具卡系統之中，導致使用者流程複雜、易出錯，且後台維護成本高、流程不合理 。

* **目的**
  將諮詢預約從現有課程/道具卡系統中獨立出來，打造一套可自動化、可追蹤、可擴充的「諮詢預約模組」，提升使用者與管理者體驗。

---

# 2. 範圍與定義

* **系統邊界**
  僅針對「諮詢預約」部分重構，其他功能（如付款、課程選購）暫不受影響。

* **使用者角色**

  * **前台使用者（學姊）**：提交作業、選擇時段、查看預約狀態
  * **後台管理者（客服／顧問）**：設定資源時段、審核預約、監控系統運行

---

# 3. 用戶故事（User Stories）

1. **學姊**

   * 作業一完成，即可在同一頁面選擇可用時段並按下「預約」。
   * 無須跳轉至課程或道具卡頁面，所有諮詢流程集中管理。

2. **後台管理者**

   * 預先設定全周／當日可預約時段，並可手動臨時開關。
   * 查看所有預約列表，快速審核並查看詳細資訊。

---

# 4. 功能性需求

| 編號 | 功能名稱                      | 說明                                                                                 |
| -- | ------------------------- | ---------------------------------------------------------------------------------- |
| 1  | 獨立諮詢模組               | 每種諮詢（十種進階指導＋活動贈送項目）皆獨立存在，非「課程」或「道具卡」 。                                             |
| 2  | 作業上傳整合               | 內建上傳區，可針對每個諮詢設定需繳交的作業項目、上傳截止時間與表單欄位。                                               |
| 3  | 預約排程功能               | 完成作業後自動解鎖預約按鈕；在同一模組頁面內選擇時段並送出，流程無跳轉。                                               |
| 4  | Zoom & Google Calendar 整合 | 預約成功後，系統自動：<br>• 建立 Zoom 會議並產生連結<br>• 同步事件至官方 Google 日曆帳戶 。                        |
| 5  | 後台小房子回報                  | 每筆預約資料自動顯示於後台「小房子」中，作為最終確認依據。                                                      |
| 6  | 時段管控                      | 支援預設週期性時段開放／關閉；預約取消後自動釋出該時段。                                                       |
| 7  | 條件自動判定                | 系統根據規則自動開通預約資格，例如：<br>• 至尊/皇家至尊＋正課＋30日內完成作業→開通「課後輔導」<br>• 使用「陪妳搭時光機」＋完成作業→顯示補課選項 。 |
| 8  | 可視化進度與狀態             | • 前台：顯示已達成條件與未完成步驟<br>• 後台：篩選並匯出預約記錄、取消情況等。                                        |

---

# 5. 非功能性需求

1. **可用性（Usability）**
   流程採「導引式頁面」，每階段明確提示，降低理解成本。

2. **可維護性（Maintainability）**
   前後端分離、模組化設計，單元測試覆蓋率 ≥ 80%。

3. **可擴充性（Extensibility）**
   新增諮詢類型時，只需配置模組參數，無需改動核心程式。

---

# 6. 系統流程概述

```mermaid
flowchart TD
  A[選擇諮詢主題] --> B[上傳作業表單]
  B --> C{作業完成?}
  C -->|否| B
  C -->|是| D[解鎖預約時段選擇]
  D --> E[送出預約申請]
  E --> F[後台小房子審核]
  F --> G[建立 Zoom & 日曆事件]
  G --> H[預約成功通知]
```
---

# 7. 資料庫設計概要

## 7.1 舊版資料庫表結構分析
- `counsel_appointments` - 諮詢預約記錄表
- `counsel_settings` - 諮詢系統設定表
- `zoom_meetings` - Zoom會議記錄表

## 7.2 新版資料庫設計

### 7.2.1 諮詢模組表 (consultation_modules)
```sql
CREATE TABLE consultation_modules (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(255) NOT NULL COMMENT '諮詢模組名稱',
    description TEXT COMMENT '模組描述',
    category ENUM('advanced_guidance', 'activity_gift') NOT NULL COMMENT '模組分類',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否啟用',
    sort_order INT DEFAULT 0 COMMENT '排序',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

### 7.2.2 作業配置表 (consultation_assignments)
```sql
CREATE TABLE consultation_assignments (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    module_id BIGINT NOT NULL,
    title VARCHAR(255) NOT NULL COMMENT '作業標題',
    description TEXT COMMENT '作業說明',
    required_fields JSON COMMENT '必填欄位配置',
    deadline_hours INT DEFAULT 72 COMMENT '上傳截止時間(小時)',
    is_required BOOLEAN DEFAULT TRUE COMMENT '是否必須完成',
    sort_order INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (module_id) REFERENCES consultation_modules(id) ON DELETE CASCADE
);
```

### 7.2.3 會員作業提交表 (member_assignment_submissions)
```sql
CREATE TABLE member_assignment_submissions (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    member_id INT UNSIGNED NOT NULL,
    assignment_id BIGINT NOT NULL,
    submission_data JSON COMMENT '提交的作業內容',
    files JSON COMMENT '上傳的檔案資訊',
    status ENUM('draft', 'submitted', 'approved', 'rejected') DEFAULT 'draft',
    submitted_at TIMESTAMP NULL,
    reviewed_at TIMESTAMP NULL,
    reviewed_by_id INT UNSIGNED NULL,
    review_note TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (member_id) REFERENCES members(id) ON DELETE CASCADE,
    FOREIGN KEY (assignment_id) REFERENCES consultation_assignments(id) ON DELETE CASCADE,
    FOREIGN KEY (reviewed_by_id) REFERENCES admins(id) ON DELETE SET NULL
);
```

### 7.2.4 新版預約表 (consultation_appointments)
```sql
CREATE TABLE consultation_appointments (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    member_id INT UNSIGNED NOT NULL,
    module_id BIGINT NOT NULL,
    submission_id BIGINT NULL COMMENT '關聯的作業提交ID',
    title VARCHAR(255) NOT NULL,
    description TEXT,
    appointment_datetime DATETIME NOT NULL,
    duration_minutes INT DEFAULT 30,
    status ENUM('pending', 'confirmed', 'completed', 'cancelled') DEFAULT 'pending',
    zoom_meeting_id VARCHAR(50) NULL,
    zoom_join_url TEXT NULL,
    google_calendar_event_id VARCHAR(255) NULL,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (member_id) REFERENCES members(id) ON DELETE CASCADE,
    FOREIGN KEY (module_id) REFERENCES consultation_modules(id) ON DELETE CASCADE,
    FOREIGN KEY (submission_id) REFERENCES member_assignment_submissions(id) ON DELETE SET NULL
);
```

### 7.2.5 時段管理表 (consultation_time_slots)
```sql
CREATE TABLE consultation_time_slots (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    date DATE NOT NULL,
    start_time TIME NOT NULL,
    end_time TIME NOT NULL,
    is_available BOOLEAN DEFAULT TRUE,
    max_appointments INT DEFAULT 1,
    created_by_id INT UNSIGNED NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by_id) REFERENCES admins(id) ON DELETE CASCADE,
    UNIQUE KEY unique_time_slot (date, start_time, end_time)
);
```

### 7.2.6 條件規則表 (consultation_eligibility_rules)
```sql
CREATE TABLE consultation_eligibility_rules (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    module_id BIGINT NOT NULL,
    rule_name VARCHAR(255) NOT NULL,
    conditions JSON NOT NULL COMMENT '條件配置',
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (module_id) REFERENCES consultation_modules(id) ON DELETE CASCADE
);
```

## 7.3 資料遷移策略
1. 保留舊版表結構，確保向下相容
2. 建立資料遷移腳本，將舊版資料轉移至新版表
3. 提供新舊系統並行運行期間的資料同步機制

---

# 8. 技術架構設計

## 8.1 Clean Architecture 分層設計

### 8.1.1 Domain Layer (領域層)
- **Entities**: 核心業務實體
  - `ConsultationModule` - 諮詢模組
  - `Assignment` - 作業
  - `Appointment` - 預約
  - `TimeSlot` - 時段
  - `EligibilityRule` - 資格規則

- **Use Cases**: 業務用例
  - `CreateConsultationModule` - 建立諮詢模組
  - `SubmitAssignment` - 提交作業
  - `BookAppointment` - 預約時段
  - `ManageTimeSlots` - 管理時段
  - `CheckEligibility` - 檢查資格

### 8.1.2 Application Layer (應用層)
- **Services**: 應用服務
  - `ConsultationService` - 諮詢服務
  - `AppointmentService` - 預約服務
  - `NotificationService` - 通知服務
  - `IntegrationService` - 整合服務 (Zoom, Google Calendar)

### 8.1.3 Infrastructure Layer (基礎設施層)
- **Repositories**: 資料存取
  - `ConsultationRepository`
  - `AppointmentRepository`
  - `AssignmentRepository`
  - `TimeSlotRepository`

- **External Services**: 外部服務
  - `ZoomService` - Zoom API 整合
  - `GoogleCalendarService` - Google Calendar API 整合
  - `EmailService` - 郵件通知服務

### 8.1.4 Interface Layer (介面層)
- **Controllers**: API 控制器
  - `ConsultationController` - 諮詢模組管理
  - `AppointmentController` - 預約管理
  - `AssignmentController` - 作業管理

## 8.2 API 設計

### 8.2.1 前台 API (會員端)
```
GET    /api/v2/consultations/modules          # 取得可用諮詢模組
GET    /api/v2/consultations/{id}/assignments # 取得模組作業要求
POST   /api/v2/assignments/submit             # 提交作業
GET    /api/v2/appointments/available-slots   # 取得可用時段
POST   /api/v2/appointments                   # 建立預約
GET    /api/v2/appointments                   # 取得我的預約
PATCH  /api/v2/appointments/{id}/confirm      # 確認預約
```

### 8.2.2 後台 API (管理端)
```
# 諮詢模組管理
GET    /api/v2/admin/consultations/modules
POST   /api/v2/admin/consultations/modules
PATCH  /api/v2/admin/consultations/modules/{id}
DELETE /api/v2/admin/consultations/modules/{id}

# 作業管理
GET    /api/v2/admin/assignments
POST   /api/v2/admin/assignments
PATCH  /api/v2/admin/assignments/{id}/review

# 預約管理
GET    /api/v2/admin/appointments
PATCH  /api/v2/admin/appointments/{id}
DELETE /api/v2/admin/appointments/{id}

# 時段管理
GET    /api/v2/admin/time-slots
POST   /api/v2/admin/time-slots
PATCH  /api/v2/admin/time-slots/{id}
DELETE /api/v2/admin/time-slots/{id}
```

---

# 9. 實施計劃

## 9.1 階段一：基礎架構建立 (第1-2週)
1. **資料庫設計與建立**
   - 建立新版資料表
   - 建立資料遷移腳本
   - 設定測試資料

2. **Domain Layer 實作**
   - 建立核心實體模型
   - 實作業務規則
   - 建立領域服務

3. **Repository Layer 實作**
   - 實作資料存取介面
   - 建立基礎 CRUD 操作
   - 實作查詢方法

## 9.2 階段二：核心功能開發 (第3-4週)
1. **諮詢模組管理**
   - 模組 CRUD 功能
   - 作業配置功能
   - 資格規則設定

2. **作業提交系統**
   - 作業上傳介面
   - 檔案管理功能
   - 審核流程

3. **預約系統核心**
   - 時段管理功能
   - 預約建立流程
   - 狀態管理

## 9.3 階段三：整合與優化 (第5-6週)
1. **外部服務整合**
   - Zoom API 整合
   - Google Calendar 整合
   - 通知系統

2. **前端介面開發**
   - 會員端諮詢頁面
   - 後台管理介面
   - 響應式設計

3. **測試與優化**
   - 單元測試 (目標80%覆蓋率)
   - 整合測試
   - 效能優化

## 9.4 階段四：部署與遷移 (第7-8週)
1. **資料遷移**
   - 舊版資料轉移
   - 資料驗證
   - 回滾機制

2. **系統部署**
   - 生產環境部署
   - 監控設定
   - 備份機制

3. **使用者培訓**
   - 操作手冊編寫
   - 使用者培訓
   - 技術文件

---

# 10. 測試策略

## 10.1 測試覆蓋率目標
- **單元測試**: ≥ 80% 程式碼覆蓋率
- **整合測試**: 覆蓋所有 API 端點
- **端到端測試**: 覆蓋主要使用者流程

## 10.2 測試分類

### 10.2.1 單元測試
- **Domain Layer 測試**
  - 實體業務邏輯測試
  - 領域服務測試
  - 業務規則驗證

- **Application Layer 測試**
  - 用例服務測試
  - 資料轉換測試
  - 錯誤處理測試

- **Repository Layer 測試**
  - 資料存取測試
  - 查詢邏輯測試
  - 資料一致性測試

### 10.2.2 整合測試
- **API 端點測試**
  - HTTP 請求/回應測試
  - 認證授權測試
  - 參數驗證測試

- **資料庫整合測試**
  - 資料持久化測試
  - 交易處理測試
  - 外鍵約束測試

- **外部服務整合測試**
  - Zoom API 整合測試
  - Google Calendar API 測試
  - 郵件服務測試

### 10.2.3 端到端測試
- **會員端流程測試**
  - 作業提交流程
  - 預約建立流程
  - 預約確認流程

- **管理端流程測試**
  - 模組管理流程
  - 預約審核流程
  - 時段管理流程

## 10.3 測試工具與框架
- **Go Testing**: 標準測試框架
- **Testify**: 斷言和模擬框架
- **Ginkgo/Gomega**: BDD 測試框架
- **Docker**: 測試環境隔離

---

# 11. 詳細任務分解

## 11.1 資料庫層任務

### 11.1.1 資料表建立
- [x] 建立 `consultation_modules` 表
- [x] 建立 `consultation_assignments` 表
- [x] 建立 `member_assignment_submissions` 表
- [x] 建立 `consultation_appointments` 表
- [x] 建立 `consultation_time_slots` 表
- [x] 建立 `consultation_eligibility_rules` 表
- [x] 建立索引和外鍵約束
- [x] 建立資料遷移腳本

### 11.1.2 資料遷移
- [x] 分析舊版資料結構
- [x] 設計資料對應關係
- [x] 實作資料轉換邏輯
- [x] 建立資料驗證機制
- [x] 測試資料遷移流程

## 11.2 Domain Layer 任務

### 11.2.1 實體模型
- [x] `ConsultationModule` 實體
- [x] `Assignment` 實體
- [x] `AssignmentSubmission` 實體
- [x] `Appointment` 實體
- [x] `TimeSlot` 實體
- [x] `EligibilityRule` 實體

### 11.2.2 領域服務
- [x] `EligibilityChecker` 服務
- [x] `AppointmentValidator` 服務
- [x] `TimeSlotManager` 服務
- [x] `AssignmentProcessor` 服務

### 11.2.3 業務規則
- [x] 預約資格檢查規則
- [x] 時段衝突檢查規則
- [x] 作業完成度驗證規則
- [x] 預約狀態轉換規則

## 11.3 Application Layer 任務

### 11.3.1 用例服務
- [x] `CreateConsultationModuleUseCase`
- [x] `SubmitAssignmentUseCase`
- [x] `BookAppointmentUseCase`
- [x] `ManageTimeSlotsUseCase`
- [x] `ReviewAssignmentUseCase`
- [x] `ConfirmAppointmentUseCase`

### 11.3.2 應用服務
- [x] `ConsultationService`
- [x] `AppointmentService`
- [x] `NotificationService`
- [x] `FileUploadService`
- [x] `IntegrationService`

## 11.4 Infrastructure Layer 任務

### 11.4.1 Repository 實作
- [x] `ConsultationModuleRepository`
- [x] `AssignmentRepository`
- [x] `AssignmentSubmissionRepository`
- [x] `AppointmentRepository`
- [x] `TimeSlotRepository`
- [x] `EligibilityRuleRepository`

### 11.4.2 外部服務整合
- [x] `ZoomService` 實作
- [x] `GoogleCalendarService` 實作
- [x] `EmailService` 實作
- [x] `FileStorageService` 實作

## 11.5 Interface Layer 任務

### 11.5.1 API 控制器
- [x] `ConsultationController` (前台)
- [x] `AppointmentController` (前台)
- [x] `AssignmentController` (前台)
- [x] `AdminConsultationController` (後台)
- [x] `AdminAppointmentController` (後台)
- [x] `AdminTimeSlotController` (後台)

### 11.5.2 前端頁面
- [x] 會員諮詢模組列表頁
- [x] 作業提交頁面
- [x] 預約時段選擇頁
- [x] 我的預約列表頁
- [x] 後台模組管理頁
- [x] 後台預約管理頁
- [x] 後台時段管理頁
- [x] 後台作業審核頁
- [x] 模組詳情頁面
- [x] 作業列表頁面

## 11.6 測試任務

### 11.6.1 單元測試
- [x] Domain Layer 測試 (目標90%覆蓋率)
- [x] Application Layer 測試 (目標85%覆蓋率)
- [x] Infrastructure Layer 測試 (目標75%覆蓋率)

### 11.6.2 整合測試
- [x] API 端點測試
- [x] 資料庫整合測試
- [x] 外部服務整合測試

### 11.6.3 端到端測試
- [x] 會員端完整流程測試
- [x] 管理端完整流程測試
- [x] 跨平台相容性測試

### 11.6.4 效能與監控測試
- [x] 負載測試工具
- [x] 效能優化腳本
- [x] 系統監控工具
- [x] 資料遷移驗證工具
- [x] 回滾機制測試

## 11.7 部署與維運任務

### 11.7.1 部署準備
- [x] 環境配置檢查
- [x] 資料庫遷移腳本
- [x] 服務啟動腳本
- [x] 監控配置

### 11.7.2 生產部署
- [x] 藍綠部署策略
- [x] 回滾機制
- [x] 健康檢查
- [x] 效能監控

### 11.7.3 工具與腳本
- [x] 負載測試工具 (`scripts/load_test.go`)
- [x] 效能優化工具 (`scripts/performance_optimization.go`)
- [x] 系統監控工具 (`scripts/system_monitor.go`)
- [x] 資料遷移驗證工具 (`scripts/migration_validator.go`)
- [x] 回滾管理工具 (`scripts/rollback_manager.go`)
- [x] 工具執行器 (`scripts/run_tool.go`)
- [x] 測試執行腳本 (`scripts/run_tests.sh` / `scripts/run_tests.bat`)

### 11.7.4 文件與培訓
- [x] API 文件編寫
- [x] 操作手冊編寫
- [x] 技術文件整理
- [x] 工具使用說明 (`scripts/README.md`)

---

# 12. 專案完成總結

## 12.1 完成狀況
✅ **100% 完成** - 所有核心功能已實現並測試完成

### 已完成的主要功能：
1. **完整的諮詢預約系統架構** - 基於 Clean Architecture 設計
2. **六個核心資料表** - 完整的資料庫設計與遷移腳本
3. **前後端完整功能** - 會員端和管理端的所有頁面
4. **外部服務整合** - Zoom、Google Calendar、郵件通知
5. **完整的測試套件** - 單元測試、整合測試、端到端測試、效能測試
6. **運維工具集** - 監控、優化、遷移、回滾等工具
7. **詳細文件** - API 文件、操作手冊、技術文件

## 12.2 技術亮點
- **分層架構設計** - 清晰的職責分離，易於維護和擴展
- **完整的測試覆蓋** - 多層次測試確保程式碼品質
- **自動化工具** - 豐富的運維和監控工具
- **安全性考量** - 檔案上傳安全、資料驗證、權限控制
- **效能優化** - 資料庫索引、查詢優化、快取機制

## 12.3 部署就緒
系統已完全準備好進行生產部署：
- ✅ 所有功能已實現並測試
- ✅ 資料庫遷移腳本已準備
- ✅ 監控和運維工具已就緒
- ✅ 回滾機制已建立
- ✅ 文件已完整

**🎉 諮詢預約系統重構專案成功完成！**