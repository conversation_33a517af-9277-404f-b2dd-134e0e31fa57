{{define "consultation_v2/module_detail"}}
<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{.Module.Name}} - G.R.A.C.E 優雅學院</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .module-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 40px 0;
            margin-bottom: 30px;
        }
        .module-info-card {
            background: white;
            border-radius: 10px;
            padding: 30px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
        }
        .assignment-card {
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            background: #fff;
            transition: all 0.2s ease;
        }
        .assignment-card:hover {
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .status-badge {
            padding: 5px 10px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
        }
        .status-not-started { background: #e2e3e5; color: #6c757d; }
        .status-pending { background: #fff3cd; color: #856404; }
        .status-approved { background: #d4edda; color: #155724; }
        .status-rejected { background: #f8d7da; color: #721c24; }
        .category-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: bold;
            background: #e3f2fd;
            color: #0d47a1;
        }
        .eligibility-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .eligibility-check {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 15px;
        }
        .eligibility-pass {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .eligibility-fail {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .progress-section {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .progress-item {
            display: flex;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #f0f0f0;
        }
        .progress-item:last-child {
            border-bottom: none;
        }
        .progress-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
        }
        .icon-completed { background: #d4edda; color: #155724; }
        .icon-current { background: #fff3cd; color: #856404; }
        .icon-pending { background: #e2e3e5; color: #6c757d; }
    </style>
</head>
<body>
    <!-- 模組標題區域 -->
    <div class="module-header">
        <div class="container">
            <div class="row">
                <div class="col-md-8 mx-auto text-center">
                    <h1>{{.Module.Name}}</h1>
                    <p class="lead">{{.Module.Description}}</p>
                    <div class="mt-3">
                        <span class="category-badge">{{getCategoryText .Module.Category}}</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <div class="row">
            <div class="col-md-8">
                <!-- 模組資訊 -->
                <div class="module-info-card">
                    <h4><i class="fas fa-info-circle"></i> 模組資訊</h4>
                    
                    {{if .Module.Requirements}}
                    <div class="mb-4">
                        <h6>參與要求</h6>
                        <p>{{.Module.Requirements}}</p>
                    </div>
                    {{end}}

                    {{if .Module.Objectives}}
                    <div class="mb-4">
                        <h6>學習目標</h6>
                        <p>{{.Module.Objectives}}</p>
                    </div>
                    {{end}}

                    <div class="row">
                        <div class="col-md-6">
                            <h6>模組詳情</h6>
                            <ul class="list-unstyled">
                                <li><i class="fas fa-tasks"></i> 作業數量：{{len .Assignments}} 個</li>
                                <li><i class="fas fa-clock"></i> 預估時間：{{calculateEstimatedTime .Assignments}} 小時</li>
                                <li><i class="fas fa-users"></i> 參與人數：{{.Module.ParticipantCount}} 人</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6>完成流程</h6>
                            <ol class="small">
                                <li>完成所有作業要求</li>
                                <li>等待作業審核通過</li>
                                <li>預約諮詢時段</li>
                                <li>參與諮詢會議</li>
                            </ol>
                        </div>
                    </div>
                </div>

                <!-- 作業列表 -->
                <div class="module-info-card">
                    <h4><i class="fas fa-clipboard-list"></i> 作業要求</h4>
                    
                    {{if .Assignments}}
                        {{range $index, $assignment := .Assignments}}
                        <div class="assignment-card">
                            <div class="d-flex justify-content-between align-items-start mb-3">
                                <div>
                                    <h6>{{$assignment.Title}}</h6>
                                    <p class="text-muted mb-2">{{$assignment.Description}}</p>
                                </div>
                                <div>
                                    {{$status := getSubmissionStatus $assignment.ID}}
                                    <span class="status-badge status-{{$status}}">
                                        {{getStatusText $status}}
                                    </span>
                                </div>
                            </div>
                            
                            {{if $assignment.Requirements}}
                            <div class="mb-3">
                                <strong>提交要求：</strong>
                                <p class="small">{{$assignment.Requirements}}</p>
                            </div>
                            {{end}}
                            
                            <div class="d-flex justify-content-between align-items-center">
                                <small class="text-muted">作業 {{add $index 1}}</small>
                                <div>
                                    {{if eq $status "not_started"}}
                                        <a href="/consultations/v2/assignments/{{$assignment.ID}}/submit" class="btn btn-primary btn-sm">
                                            <i class="fas fa-upload"></i> 開始提交
                                        </a>
                                    {{else if eq $status "rejected"}}
                                        <a href="/consultations/v2/assignments/{{$assignment.ID}}/submit" class="btn btn-warning btn-sm">
                                            <i class="fas fa-edit"></i> 重新提交
                                        </a>
                                    {{else if eq $status "pending"}}
                                        <a href="/consultations/v2/assignments/{{$assignment.ID}}/submit" class="btn btn-outline-secondary btn-sm">
                                            <i class="fas fa-eye"></i> 查看提交
                                        </a>
                                    {{else}}
                                        <span class="text-success small">
                                            <i class="fas fa-check-circle"></i> 已完成
                                        </span>
                                    {{end}}
                                </div>
                            </div>
                        </div>
                        {{end}}
                    {{else}}
                        <div class="text-center py-4">
                            <i class="fas fa-clipboard fa-3x text-muted mb-3"></i>
                            <h6>此模組暫無作業要求</h6>
                            <p class="text-muted">您可以直接進行預約</p>
                        </div>
                    {{end}}
                </div>
            </div>

            <div class="col-md-4">
                <!-- 資格檢查 -->
                <div class="eligibility-section">
                    <h5><i class="fas fa-shield-alt"></i> 預約資格</h5>
                    
                    {{if .Eligible}}
                        <div class="eligibility-check eligibility-pass">
                            <i class="fas fa-check-circle"></i>
                            <strong>符合預約資格</strong>
                            <p class="mb-0 small">您已滿足所有預約條件，可以進行預約。</p>
                        </div>
                        
                        <div class="text-center mt-3">
                            <a href="/consultations/v2/book?module_id={{.Module.ID}}" class="btn btn-success btn-lg w-100">
                                <i class="fas fa-calendar-plus"></i> 立即預約
                            </a>
                        </div>
                    {{else}}
                        <div class="eligibility-check eligibility-fail">
                            <i class="fas fa-exclamation-triangle"></i>
                            <strong>尚未符合預約資格</strong>
                            <p class="mb-0 small">{{.EligibilityMessage}}</p>
                        </div>
                        
                        <div class="text-center mt-3">
                            <button class="btn btn-secondary btn-lg w-100" disabled>
                                <i class="fas fa-lock"></i> 暫無法預約
                            </button>
                        </div>
                    {{end}}
                </div>

                <!-- 進度追蹤 -->
                <div class="progress-section">
                    <h5><i class="fas fa-chart-line"></i> 完成進度</h5>
                    
                    <div id="progressList">
                        <!-- 進度項目將由 JavaScript 動態生成 -->
                    </div>
                    
                    <div class="mt-3">
                        <div class="progress">
                            <div class="progress-bar" role="progressbar" style="width: {{calculateProgress .Assignments}}%" 
                                 aria-valuenow="{{calculateProgress .Assignments}}" aria-valuemin="0" aria-valuemax="100">
                                {{calculateProgress .Assignments}}%
                            </div>
                        </div>
                        <small class="text-muted">整體完成度</small>
                    </div>
                </div>

                <!-- 相關連結 -->
                <div class="progress-section">
                    <h5><i class="fas fa-link"></i> 相關連結</h5>
                    
                    <div class="d-grid gap-2">
                        <a href="/consultations/v2/modules/{{.Module.ID}}/assignments" class="btn btn-outline-primary">
                            <i class="fas fa-list"></i> 查看所有作業
                        </a>
                        <a href="/consultations/v2/appointments" class="btn btn-outline-info">
                            <i class="fas fa-calendar-check"></i> 我的預約記錄
                        </a>
                        <a href="/consultations/v2" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left"></i> 返回模組列表
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            renderProgressList();
        });

        // 渲染進度列表
        function renderProgressList() {
            const assignments = {{.Assignments | toJSON}};
            const submissionStatus = {{.SubmissionStatus | toJSON}};
            
            const progressList = document.getElementById('progressList');
            let progressHTML = '';

            // 作業完成進度
            assignments.forEach((assignment, index) => {
                const status = submissionStatus[assignment.id] || 'not_started';
                const iconClass = getProgressIconClass(status);
                const statusText = getProgressStatusText(status);
                
                progressHTML += `
                    <div class="progress-item">
                        <div class="progress-icon ${iconClass}">
                            <i class="fas fa-${getProgressIcon(status)}"></i>
                        </div>
                        <div class="flex-grow-1">
                            <div class="fw-bold">${assignment.title}</div>
                            <div class="small text-muted">${statusText}</div>
                        </div>
                    </div>
                `;
            });

            // 預約進度
            const eligible = {{.Eligible}};
            const appointmentIconClass = eligible ? 'icon-current' : 'icon-pending';
            const appointmentIcon = eligible ? 'calendar-plus' : 'lock';
            const appointmentText = eligible ? '可以預約' : '等待資格確認';
            
            progressHTML += `
                <div class="progress-item">
                    <div class="progress-icon ${appointmentIconClass}">
                        <i class="fas fa-${appointmentIcon}"></i>
                    </div>
                    <div class="flex-grow-1">
                        <div class="fw-bold">預約諮詢</div>
                        <div class="small text-muted">${appointmentText}</div>
                    </div>
                </div>
            `;

            progressList.innerHTML = progressHTML;
        }

        // 取得進度圖示類別
        function getProgressIconClass(status) {
            switch (status) {
                case 'approved':
                    return 'icon-completed';
                case 'pending':
                    return 'icon-current';
                case 'rejected':
                    return 'icon-current';
                default:
                    return 'icon-pending';
            }
        }

        // 取得進度圖示
        function getProgressIcon(status) {
            switch (status) {
                case 'approved':
                    return 'check';
                case 'pending':
                    return 'clock';
                case 'rejected':
                    return 'edit';
                default:
                    return 'circle';
            }
        }

        // 取得進度狀態文字
        function getProgressStatusText(status) {
            switch (status) {
                case 'approved':
                    return '已通過';
                case 'pending':
                    return '審核中';
                case 'rejected':
                    return '需修改';
                default:
                    return '未開始';
            }
        }

        // 輔助函數
        function getCategoryText(category) {
            const categoryMap = {
                'basic_guidance': '基礎指導',
                'advanced_guidance': '進階指導',
                'specialized_consultation': '專業諮詢',
                'group_session': '團體課程',
                'activity_gift': '活動禮品'
            };
            return categoryMap[category] || category;
        }

        function getStatusText(status) {
            const statusMap = {
                'not_started': '未開始',
                'pending': '審核中',
                'approved': '已通過',
                'rejected': '需修改'
            };
            return statusMap[status] || status;
        }

        function calculateEstimatedTime(assignments) {
            // 簡單估算：每個作業約需 2 小時
            return assignments.length * 2;
        }

        function calculateProgress(assignments) {
            if (assignments.length === 0) return 100;
            
            const submissionStatus = {{.SubmissionStatus | toJSON}};
            let completedCount = 0;
            
            assignments.forEach(assignment => {
                const status = submissionStatus[assignment.id];
                if (status === 'approved') {
                    completedCount++;
                }
            });
            
            return Math.round((completedCount / assignments.length) * 100);
        }
    </script>
</body>
</html>
{{end}}
