//go:build ignore
// +build ignore

package main

import (
	"bufio"
	"fmt"
	"log"
	"os"
	"strings"
	"time"

	"gorm.io/driver/mysql"
	"gorm.io/gorm"

	. "cx/app/models"
)

// RollbackManager 回滾管理器
type RollbackManager struct {
	db         *gorm.DB
	backupPath string
}

// BackupInfo 備份資訊
type BackupInfo struct {
	Timestamp   time.Time
	Version     string
	Description string
	Tables      []string
	FilePath    string
}

// NewRollbackManager 建立回滾管理器
func NewRollbackManager() *RollbackManager {
	// 連接資料庫
	dsn := "root:forwork0926@tcp(localhost:3306)/grace?parseTime=True&loc=Local&charset=utf8mb4&collation=utf8mb4_unicode_ci"
	db, err := gorm.Open(mysql.Open(dsn), &gorm.Config{})
	if err != nil {
		log.Fatal("連接資料庫失敗:", err)
	}

	return &RollbackManager{
		db:         db,
		backupPath: "./backups",
	}
}

// CreateBackup 建立備份
func (rm *RollbackManager) CreateBackup(description string) (*BackupInfo, error) {
	fmt.Println("📦 建立資料備份...")

	// 確保備份目錄存在
	if err := os.MkdirAll(rm.backupPath, 0755); err != nil {
		return nil, fmt.Errorf("建立備份目錄失敗: %w", err)
	}

	timestamp := time.Now()
	backupFile := fmt.Sprintf("%s/backup_%s.sql", rm.backupPath, timestamp.Format("20060102_150405"))

	// 取得要備份的表格列表
	tables := []string{
		"consultation_modules",
		"consultation_assignments",
		"member_assignment_submissions",
		"consultation_appointments",
		"consultation_time_slots",
		"consultation_eligibility_rules",
		"file_uploads",
		"members", // 如果需要的話
	}

	// 執行 mysqldump
	cmd := fmt.Sprintf("mysqldump -h localhost -u root -pforwork0926 grace %s > %s",
		strings.Join(tables, " "), backupFile)

	if err := rm.executeCommand(cmd); err != nil {
		return nil, fmt.Errorf("執行備份失敗: %w", err)
	}

	backupInfo := &BackupInfo{
		Timestamp:   timestamp,
		Version:     "v2.0",
		Description: description,
		Tables:      tables,
		FilePath:    backupFile,
	}

	// 保存備份資訊
	if err := rm.saveBackupInfo(backupInfo); err != nil {
		return nil, fmt.Errorf("保存備份資訊失敗: %w", err)
	}

	fmt.Printf("✅ 備份完成: %s\n", backupFile)
	return backupInfo, nil
}

// ListBackups 列出可用備份
func (rm *RollbackManager) ListBackups() ([]BackupInfo, error) {
	fmt.Println("📋 查詢可用備份...")

	// 讀取備份目錄
	files, err := os.ReadDir(rm.backupPath)
	if err != nil {
		return nil, fmt.Errorf("讀取備份目錄失敗: %w", err)
	}

	var backups []BackupInfo
	for _, file := range files {
		if strings.HasSuffix(file.Name(), ".sql") {
			// 解析檔案名稱取得時間戳
			if strings.HasPrefix(file.Name(), "backup_") {
				timeStr := strings.TrimPrefix(file.Name(), "backup_")
				timeStr = strings.TrimSuffix(timeStr, ".sql")

				if timestamp, err := time.Parse("20060102_150405", timeStr); err == nil {
					backup := BackupInfo{
						Timestamp: timestamp,
						FilePath:  fmt.Sprintf("%s/%s", rm.backupPath, file.Name()),
					}

					// 嘗試讀取備份資訊
					if info, err := rm.loadBackupInfo(backup.FilePath); err == nil {
						backup = *info
					}

					backups = append(backups, backup)
				}
			}
		}
	}

	return backups, nil
}

// ExecuteRollback 執行回滾
func (rm *RollbackManager) ExecuteRollback(backupFile string) error {
	fmt.Printf("🔄 開始回滾到備份: %s\n", backupFile)

	// 確認操作
	if !rm.confirmRollback() {
		fmt.Println("❌ 回滾操作已取消")
		return nil
	}

	// 建立當前狀態的緊急備份
	fmt.Println("📦 建立緊急備份...")
	emergencyBackup, err := rm.CreateBackup("回滾前緊急備份")
	if err != nil {
		return fmt.Errorf("建立緊急備份失敗: %w", err)
	}
	fmt.Printf("✅ 緊急備份完成: %s\n", emergencyBackup.FilePath)

	// 停用外鍵檢查
	fmt.Println("🔧 停用外鍵檢查...")
	if err := rm.db.Exec("SET FOREIGN_KEY_CHECKS = 0").Error; err != nil {
		return fmt.Errorf("停用外鍵檢查失敗: %w", err)
	}

	// 清空現有表格
	fmt.Println("🗑️  清空現有表格...")
	tables := []string{
		"consultation_eligibility_rules",
		"member_assignment_submissions",
		"consultation_appointments",
		"consultation_time_slots",
		"consultation_assignments",
		"consultation_modules",
		"file_uploads",
	}

	for _, table := range tables {
		if err := rm.db.Exec(fmt.Sprintf("TRUNCATE TABLE %s", table)).Error; err != nil {
			fmt.Printf("⚠️  清空表格 %s 失敗: %v\n", table, err)
		} else {
			fmt.Printf("✅ 清空表格: %s\n", table)
		}
	}

	// 恢復備份資料
	fmt.Println("📥 恢復備份資料...")
	cmd := fmt.Sprintf("mysql -h localhost -u root -pforwork0926 grace < %s", backupFile)
	if err := rm.executeCommand(cmd); err != nil {
		// 如果恢復失敗，嘗試恢復緊急備份
		fmt.Println("❌ 恢復備份失敗，嘗試恢復緊急備份...")
		emergencyCmd := fmt.Sprintf("mysql -h localhost -u root -pforwork0926 grace < %s", emergencyBackup.FilePath)
		if emergencyErr := rm.executeCommand(emergencyCmd); emergencyErr != nil {
			return fmt.Errorf("恢復失敗且緊急恢復也失敗: 原錯誤=%w, 緊急恢復錯誤=%v", err, emergencyErr)
		}
		return fmt.Errorf("恢復備份失敗，已恢復到緊急備份狀態: %w", err)
	}

	// 重新啟用外鍵檢查
	fmt.Println("🔧 重新啟用外鍵檢查...")
	if err := rm.db.Exec("SET FOREIGN_KEY_CHECKS = 1").Error; err != nil {
		fmt.Printf("⚠️  重新啟用外鍵檢查失敗: %v\n", err)
	}

	// 驗證回滾結果
	fmt.Println("🔍 驗證回滾結果...")
	if err := rm.validateRollback(); err != nil {
		return fmt.Errorf("回滾驗證失敗: %w", err)
	}

	fmt.Println("✅ 回滾完成！")
	return nil
}

// confirmRollback 確認回滾操作
func (rm *RollbackManager) confirmRollback() bool {
	fmt.Println("⚠️  警告: 此操作將會覆蓋現有資料！")
	fmt.Println("⚠️  建議在執行前確保已有完整備份！")
	fmt.Print("確定要繼續回滾嗎？(yes/no): ")

	reader := bufio.NewReader(os.Stdin)
	input, _ := reader.ReadString('\n')
	input = strings.TrimSpace(strings.ToLower(input))

	return input == "yes" || input == "y"
}

// validateRollback 驗證回滾結果
func (rm *RollbackManager) validateRollback() error {
	// 檢查關鍵表格是否存在資料
	tables := map[string]string{
		"consultation_modules":     "諮詢模組",
		"consultation_assignments": "作業配置",
		"consultation_time_slots":  "時段設定",
	}

	for table, desc := range tables {
		var count int64
		if err := rm.db.Table(table).Count(&count).Error; err != nil {
			return fmt.Errorf("檢查表格 %s 失敗: %w", table, err)
		}
		fmt.Printf("✅ %s: %d 筆記錄\n", desc, count)
	}

	// 檢查資料完整性
	var orphanAssignments int64
	rm.db.Raw(`
		SELECT COUNT(*) FROM consultation_assignments ca
		LEFT JOIN consultation_modules cm ON ca.module_id = cm.id
		WHERE cm.id IS NULL
	`).Scan(&orphanAssignments)

	if orphanAssignments > 0 {
		return fmt.Errorf("發現 %d 個孤立的作業記錄", orphanAssignments)
	}

	fmt.Println("✅ 資料完整性檢查通過")
	return nil
}

// executeCommand 執行系統命令
func (rm *RollbackManager) executeCommand(cmd string) error {
	// 這裡應該使用 os/exec 包來執行命令
	// 為了簡化，這裡只是模擬
	fmt.Printf("執行命令: %s\n", cmd)

	// 實際實作中應該使用:
	// exec.Command("sh", "-c", cmd).Run()

	return nil
}

// saveBackupInfo 保存備份資訊
func (rm *RollbackManager) saveBackupInfo(info *BackupInfo) error {
	// 實作保存備份資訊到檔案或資料庫
	infoFile := strings.Replace(info.FilePath, ".sql", ".info", 1)

	content := fmt.Sprintf(`備份時間: %s
版本: %s
描述: %s
表格: %s
`, info.Timestamp.Format("2006-01-02 15:04:05"), info.Version, info.Description, strings.Join(info.Tables, ", "))

	return os.WriteFile(infoFile, []byte(content), 0644)
}

// loadBackupInfo 載入備份資訊
func (rm *RollbackManager) loadBackupInfo(backupFile string) (*BackupInfo, error) {
	infoFile := strings.Replace(backupFile, ".sql", ".info", 1)

	if _, err := os.Stat(infoFile); os.IsNotExist(err) {
		return nil, err
	}

	// 簡化實作，實際應該解析檔案內容
	return &BackupInfo{
		FilePath:    backupFile,
		Description: "備份",
		Version:     "v2.0",
	}, nil
}

// ShowMenu 顯示主選單
func (rm *RollbackManager) ShowMenu() {
	for {
		fmt.Println("\n🔄 回滾管理器")
		fmt.Println("=============")
		fmt.Println("1. 建立備份")
		fmt.Println("2. 列出備份")
		fmt.Println("3. 執行回滾")
		fmt.Println("4. 清理舊備份")
		fmt.Println("5. 退出")
		fmt.Print("請選擇操作 (1-5): ")

		var choice string
		fmt.Scanln(&choice)

		switch choice {
		case "1":
			rm.handleCreateBackup()
		case "2":
			rm.handleListBackups()
		case "3":
			rm.handleExecuteRollback()
		case "4":
			rm.handleCleanupBackups()
		case "5":
			fmt.Println("👋 再見！")
			return
		default:
			fmt.Println("❌ 無效選擇，請重試")
		}
	}
}

// handleCreateBackup 處理建立備份
func (rm *RollbackManager) handleCreateBackup() {
	fmt.Print("請輸入備份描述: ")
	reader := bufio.NewReader(os.Stdin)
	description, _ := reader.ReadString('\n')
	description = strings.TrimSpace(description)

	if description == "" {
		description = "手動備份"
	}

	backup, err := rm.CreateBackup(description)
	if err != nil {
		fmt.Printf("❌ 建立備份失敗: %v\n", err)
		return
	}

	fmt.Printf("✅ 備份建立成功: %s\n", backup.FilePath)
}

// handleListBackups 處理列出備份
func (rm *RollbackManager) handleListBackups() {
	backups, err := rm.ListBackups()
	if err != nil {
		fmt.Printf("❌ 列出備份失敗: %v\n", err)
		return
	}

	if len(backups) == 0 {
		fmt.Println("📭 沒有找到備份檔案")
		return
	}

	fmt.Println("\n📋 可用備份:")
	fmt.Println("編號 | 時間                | 描述")
	fmt.Println("----|--------------------|---------")

	for i, backup := range backups {
		fmt.Printf("%-4d| %s | %s\n",
			i+1,
			backup.Timestamp.Format("2006-01-02 15:04:05"),
			backup.Description)
	}
}

// handleExecuteRollback 處理執行回滾
func (rm *RollbackManager) handleExecuteRollback() {
	backups, err := rm.ListBackups()
	if err != nil {
		fmt.Printf("❌ 列出備份失敗: %v\n", err)
		return
	}

	if len(backups) == 0 {
		fmt.Println("📭 沒有找到備份檔案")
		return
	}

	// 顯示備份列表
	rm.handleListBackups()

	fmt.Print("請選擇要回滾的備份編號: ")
	var choice int
	fmt.Scanln(&choice)

	if choice < 1 || choice > len(backups) {
		fmt.Println("❌ 無效的備份編號")
		return
	}

	selectedBackup := backups[choice-1]
	if err := rm.ExecuteRollback(selectedBackup.FilePath); err != nil {
		fmt.Printf("❌ 回滾失敗: %v\n", err)
	}
}

// handleCleanupBackups 處理清理舊備份
func (rm *RollbackManager) handleCleanupBackups() {
	fmt.Println("🧹 清理30天前的備份...")

	backups, err := rm.ListBackups()
	if err != nil {
		fmt.Printf("❌ 列出備份失敗: %v\n", err)
		return
	}

	cutoff := time.Now().AddDate(0, 0, -30)
	cleaned := 0

	for _, backup := range backups {
		if backup.Timestamp.Before(cutoff) {
			if err := os.Remove(backup.FilePath); err != nil {
				fmt.Printf("⚠️  刪除備份失敗: %s - %v\n", backup.FilePath, err)
			} else {
				fmt.Printf("🗑️  已刪除: %s\n", backup.FilePath)
				cleaned++
			}
		}
	}

	fmt.Printf("✅ 清理完成，共刪除 %d 個舊備份\n", cleaned)
}

func main() {
	fmt.Println("🔄 諮詢系統回滾管理器")
	fmt.Println("====================")
	fmt.Println("此工具用於管理資料備份和回滾操作")
	fmt.Println("⚠️  請謹慎使用，確保在安全環境中操作")

	manager := NewRollbackManager()
	manager.ShowMenu()
}
