@echo off
setlocal enabledelayedexpansion

REM 諮詢系統 V2 測試運行腳本 (Windows)

echo === 諮詢系統 V2 測試套件 ===
echo 開始時間: %date% %time%
echo.

REM 檢查環境
echo 檢查測試環境...

REM 檢查 Go 版本
go version >nul 2>&1
if errorlevel 1 (
    echo 錯誤: Go 未安裝
    exit /b 1
)

for /f "tokens=*" %%i in ('go version') do set GO_VERSION=%%i
echo Go 版本: %GO_VERSION%

REM 檢查 MySQL 連接
echo 檢查測試資料庫連接...
mysql -h localhost -u root -pforwork0926 -e "SELECT 1" >nul 2>&1
if errorlevel 1 (
    echo 錯誤: 無法連接到測試資料庫
    echo 請確認 MySQL 服務正在運行且認證資訊正確
    exit /b 1
)

REM 建立測試資料庫
echo 準備測試資料庫...
mysql -h localhost -u root -pforwork0926 -e "CREATE DATABASE IF NOT EXISTS grace_test CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"

echo 測試環境檢查完成
echo.

REM 設置測試環境變數
set GO_ENV=test
set DB_NAME=grace_test

REM 清理之前的測試結果
echo 清理測試環境...
if exist test_uploads rmdir /s /q test_uploads
if exist coverage.out del coverage.out
if exist test_results.xml del test_results.xml
if exist coverage_*.out del coverage_*.out

REM 下載測試依賴
echo 下載測試依賴...
go mod download
go mod tidy

echo.
echo === 開始運行測試 ===
echo.

REM 檢查測試類型參數
set TEST_TYPE=%1
if "%TEST_TYPE%"=="" set TEST_TYPE=all

if "%TEST_TYPE%"=="unit" (
    echo 1. 運行單元測試...
    echo ----------------------------------------
    go test -v -race -coverprofile=coverage_unit.out ./test/consultation_v2_test.go
    if errorlevel 1 (
        echo 單元測試失敗
        exit /b 1
    )
    goto :generate_reports
)

if "%TEST_TYPE%"=="integration" (
    echo 2. 運行整合測試...
    echo ----------------------------------------
    go test -v -race -coverprofile=coverage_integration.out ./test/integration_test.go
    if errorlevel 1 (
        echo 整合測試失敗
        exit /b 1
    )
    goto :generate_reports
)

if "%TEST_TYPE%"=="e2e" (
    echo 3. 運行端到端測試...
    echo ----------------------------------------
    go test -v -race -coverprofile=coverage_e2e.out ./test/e2e_test.go
    if errorlevel 1 (
        echo 端到端測試失敗
        exit /b 1
    )
    goto :generate_reports
)

if "%TEST_TYPE%"=="performance" (
    echo 4. 運行效能測試...
    echo ----------------------------------------
    go test -v -timeout=10m ./test/performance_test.go
    if errorlevel 1 (
        echo 效能測試失敗
        exit /b 1
    )
    goto :end
)

if "%TEST_TYPE%"=="benchmark" (
    echo 5. 運行基準測試...
    echo ----------------------------------------
    go test -bench=. -benchmem ./test/performance_test.go > benchmark_results.txt
    if errorlevel 1 (
        echo 基準測試失敗
        exit /b 1
    )
    goto :end
)

REM 執行所有測試（預設）
echo 1. 運行單元測試...
echo ----------------------------------------
go test -v -race -coverprofile=coverage_unit.out ./test/consultation_v2_test.go
if errorlevel 1 (
    echo 單元測試失敗
    exit /b 1
)

echo.
echo 2. 運行整合測試...
echo ----------------------------------------
go test -v -race -coverprofile=coverage_integration.out ./test/integration_test.go
if errorlevel 1 (
    echo 整合測試失敗
    exit /b 1
)

echo.
echo 3. 運行端到端測試...
echo ----------------------------------------
go test -v -race -coverprofile=coverage_e2e.out ./test/e2e_test.go
if errorlevel 1 (
    echo 端到端測試失敗
    exit /b 1
)

echo.
echo 4. 運行效能測試...
echo ----------------------------------------
go test -v -timeout=10m ./test/performance_test.go
if errorlevel 1 (
    echo 效能測試失敗
    exit /b 1
)

echo.
echo 5. 運行基準測試...
echo ----------------------------------------
go test -bench=. -benchmem ./test/performance_test.go > benchmark_results.txt
if errorlevel 1 (
    echo 基準測試失敗
    exit /b 1
)

:generate_reports

REM 生成測試報告（僅在執行所有測試時）
if "%TEST_TYPE%"=="all" (
    echo.
    echo === 生成測試報告 ===
    echo.

    REM 合併覆蓋率文件
    echo 合併覆蓋率報告...
    echo mode: atomic > coverage.out

    REM 合併所有覆蓋率文件（跳過 mode 行）
    for %%f in (coverage_*.out) do (
        for /f "skip=1 tokens=*" %%l in (%%f) do (
            echo %%l >> coverage.out
        )
    )

    REM 生成覆蓋率報告
    if exist coverage.out (
        echo 生成覆蓋率報告...
        go tool cover -html=coverage.out -o coverage.html

        REM 顯示覆蓋率統計
        echo.
        echo 覆蓋率統計:
        go tool cover -func=coverage.out | findstr "total:"

        echo.
        echo 詳細覆蓋率報告已生成: coverage.html
    )

    REM 清理臨時文件
    del coverage_*.out >nul 2>&1
)

:end
echo.
echo === 測試完成 ===
echo 結束時間: %date% %time%
echo.

echo ✅ 測試通過！
echo.

if "%TEST_TYPE%"=="all" (
    echo 📊 生成的報告:
    echo - 覆蓋率報告: coverage.html
    echo - 基準測試結果: benchmark_results.txt
    echo.
    echo 下一步建議:
    echo 1. 檢查覆蓋率報告 ^(coverage.html^)
    echo 2. 運行資料遷移測試
    echo 3. 進行手動功能測試
    echo 4. 準備部署到測試環境
)

echo.
echo 💡 使用方法:
echo   run_tests.bat unit        - 只執行單元測試
echo   run_tests.bat integration - 只執行整合測試
echo   run_tests.bat e2e         - 只執行端到端測試
echo   run_tests.bat performance - 只執行效能測試
echo   run_tests.bat benchmark   - 只執行基準測試
echo   run_tests.bat all         - 執行所有測試（預設）

pause
