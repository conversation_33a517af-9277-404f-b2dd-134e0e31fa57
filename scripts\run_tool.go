// +build ignore

package main

import (
	"fmt"
	"os"
	"os/exec"
	"path/filepath"
)

func main() {
	if len(os.Args) < 2 {
		showUsage()
		return
	}

	toolName := os.Args[1]
	
	// 取得腳本目錄
	scriptDir, err := filepath.Abs(filepath.Dir(os.Args[0]))
	if err != nil {
		fmt.Printf("錯誤: 無法取得腳本目錄: %v\n", err)
		os.Exit(1)
	}

	var toolPath string
	switch toolName {
	case "load-test":
		toolPath = filepath.Join(scriptDir, "load_test.go")
	case "migration-validator":
		toolPath = filepath.Join(scriptDir, "migration_validator.go")
	case "performance-optimization":
		toolPath = filepath.Join(scriptDir, "performance_optimization.go")
	case "rollback-manager":
		toolPath = filepath.Join(scriptDir, "rollback_manager.go")
	case "system-monitor":
		toolPath = filepath.Join(scriptDir, "system_monitor.go")
	default:
		fmt.Printf("錯誤: 未知的工具 '%s'\n", toolName)
		showUsage()
		os.Exit(1)
	}

	// 檢查工具檔案是否存在
	if _, err := os.Stat(toolPath); os.IsNotExist(err) {
		fmt.Printf("錯誤: 工具檔案不存在: %s\n", toolPath)
		os.Exit(1)
	}

	// 執行工具
	fmt.Printf("🚀 執行工具: %s\n", toolName)
	fmt.Println("==================")

	cmd := exec.Command("go", "run", toolPath)
	cmd.Stdout = os.Stdout
	cmd.Stderr = os.Stderr
	cmd.Stdin = os.Stdin

	if err := cmd.Run(); err != nil {
		fmt.Printf("錯誤: 執行工具失敗: %v\n", err)
		os.Exit(1)
	}
}

func showUsage() {
	fmt.Println("🛠️  諮詢系統工具執行器")
	fmt.Println("====================")
	fmt.Println("用法: go run scripts/run_tool.go <工具名稱>")
	fmt.Println("")
	fmt.Println("可用工具:")
	fmt.Println("  load-test              - 負載測試工具")
	fmt.Println("  migration-validator    - 資料遷移驗證工具")
	fmt.Println("  performance-optimization - 效能優化工具")
	fmt.Println("  rollback-manager       - 回滾管理工具")
	fmt.Println("  system-monitor         - 系統監控工具")
	fmt.Println("")
	fmt.Println("範例:")
	fmt.Println("  go run scripts/run_tool.go load-test")
	fmt.Println("  go run scripts/run_tool.go system-monitor")
}
