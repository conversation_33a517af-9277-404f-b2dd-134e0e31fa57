//go:build ignore
// +build ignore

package main

import (
	"fmt"
	"log"
	"time"

	"gorm.io/driver/mysql"
	"gorm.io/gorm"

	. "cx/app/models"
)

// MigrationValidator 資料遷移驗證器
type MigrationValidator struct {
	db *gorm.DB
}

// ValidationResult 驗證結果
type ValidationResult struct {
	TableName      string
	TotalRecords   int64
	ValidRecords   int64
	InvalidRecords int64
	Errors         []string
	Warnings       []string
}

// MigrationReport 遷移報告
type MigrationReport struct {
	StartTime time.Time
	EndTime   time.Time
	Duration  time.Duration
	Results   []ValidationResult
	Summary   ValidationSummary
}

// ValidationSummary 驗證摘要
type ValidationSummary struct {
	TotalTables    int
	PassedTables   int
	FailedTables   int
	TotalRecords   int64
	ValidRecords   int64
	InvalidRecords int64
	OverallSuccess bool
}

// NewMigrationValidator 建立遷移驗證器
func NewMigrationValidator() *MigrationValidator {
	// 連接資料庫
	dsn := "root:forwork0926@tcp(localhost:3306)/grace?parseTime=True&loc=Local&charset=utf8mb4&collation=utf8mb4_unicode_ci"
	db, err := gorm.Open(mysql.Open(dsn), &gorm.Config{})
	if err != nil {
		log.Fatal("連接資料庫失敗:", err)
	}

	return &MigrationValidator{db: db}
}

// ValidateAllTables 驗證所有表格
func (mv *MigrationValidator) ValidateAllTables() (*MigrationReport, error) {
	fmt.Println("🔍 開始資料遷移驗證...")

	report := &MigrationReport{
		StartTime: time.Now(),
		Results:   make([]ValidationResult, 0),
	}

	// 驗證各個表格
	validators := []func() ValidationResult{
		mv.validateConsultationModules,
		mv.validateConsultationAssignments,
		mv.validateMemberAssignmentSubmissions,
		mv.validateConsultationAppointments,
		mv.validateConsultationTimeSlots,
		mv.validateConsultationEligibilityRules,
		mv.validateFileUploads,
		mv.validateDataIntegrity,
	}

	for _, validator := range validators {
		result := validator()
		report.Results = append(report.Results, result)
		fmt.Printf("✅ 完成驗證: %s\n", result.TableName)
	}

	report.EndTime = time.Now()
	report.Duration = report.EndTime.Sub(report.StartTime)
	report.Summary = mv.calculateSummary(report.Results)

	return report, nil
}

// validateConsultationModules 驗證諮詢模組表
func (mv *MigrationValidator) validateConsultationModules() ValidationResult {
	result := ValidationResult{
		TableName: "consultation_modules",
		Errors:    make([]string, 0),
		Warnings:  make([]string, 0),
	}

	// 計算總記錄數
	mv.db.Model(&ConsultationModule{}).Count(&result.TotalRecords)

	// 驗證必填欄位
	var invalidCount int64
	mv.db.Model(&ConsultationModule{}).Where("name = '' OR name IS NULL").Count(&invalidCount)
	if invalidCount > 0 {
		result.Errors = append(result.Errors, fmt.Sprintf("發現 %d 個模組名稱為空", invalidCount))
	}

	// 驗證分類欄位
	mv.db.Model(&ConsultationModule{}).Where("category NOT IN (?, ?, ?, ?)",
		"basic_guidance", "advanced_guidance", "specialized_consultation", "group_session").Count(&invalidCount)
	if invalidCount > 0 {
		result.Errors = append(result.Errors, fmt.Sprintf("發現 %d 個無效的模組分類", invalidCount))
	}

	// 驗證排序欄位
	mv.db.Model(&ConsultationModule{}).Where("sort_order < 1").Count(&invalidCount)
	if invalidCount > 0 {
		result.Warnings = append(result.Warnings, fmt.Sprintf("發現 %d 個無效的排序值", invalidCount))
	}

	// 檢查重複的排序值
	var duplicateSorts []struct {
		SortOrder int
		Count     int64
	}
	mv.db.Model(&ConsultationModule{}).Select("sort_order, COUNT(*) as count").
		Group("sort_order").Having("COUNT(*) > 1").Scan(&duplicateSorts)

	if len(duplicateSorts) > 0 {
		result.Warnings = append(result.Warnings, fmt.Sprintf("發現 %d 個重複的排序值", len(duplicateSorts)))
	}

	result.InvalidRecords = int64(len(result.Errors))
	result.ValidRecords = result.TotalRecords - result.InvalidRecords

	return result
}

// validateConsultationAssignments 驗證作業表
func (mv *MigrationValidator) validateConsultationAssignments() ValidationResult {
	result := ValidationResult{
		TableName: "consultation_assignments",
		Errors:    make([]string, 0),
		Warnings:  make([]string, 0),
	}

	mv.db.Model(&ConsultationAssignment{}).Count(&result.TotalRecords)

	// 驗證外鍵關聯
	var orphanCount int64
	mv.db.Model(&ConsultationAssignment{}).
		Where("module_id NOT IN (SELECT id FROM consultation_modules)").
		Count(&orphanCount)
	if orphanCount > 0 {
		result.Errors = append(result.Errors, fmt.Sprintf("發現 %d 個孤立的作業記錄", orphanCount))
	}

	// 驗證必填欄位
	var invalidCount int64
	mv.db.Model(&ConsultationAssignment{}).Where("title = '' OR title IS NULL").Count(&invalidCount)
	if invalidCount > 0 {
		result.Errors = append(result.Errors, fmt.Sprintf("發現 %d 個作業標題為空", invalidCount))
	}

	result.InvalidRecords = int64(len(result.Errors))
	result.ValidRecords = result.TotalRecords - result.InvalidRecords

	return result
}

// validateMemberAssignmentSubmissions 驗證作業提交表
func (mv *MigrationValidator) validateMemberAssignmentSubmissions() ValidationResult {
	result := ValidationResult{
		TableName: "member_assignment_submissions",
		Errors:    make([]string, 0),
		Warnings:  make([]string, 0),
	}

	mv.db.Model(&MemberAssignmentSubmission{}).Count(&result.TotalRecords)

	// 驗證外鍵關聯
	var orphanAssignments int64
	mv.db.Model(&MemberAssignmentSubmission{}).
		Where("assignment_id NOT IN (SELECT id FROM consultation_assignments)").
		Count(&orphanAssignments)
	if orphanAssignments > 0 {
		result.Errors = append(result.Errors, fmt.Sprintf("發現 %d 個孤立的作業提交記錄", orphanAssignments))
	}

	var orphanMembers int64
	mv.db.Model(&MemberAssignmentSubmission{}).
		Where("member_id NOT IN (SELECT id FROM members)").
		Count(&orphanMembers)
	if orphanMembers > 0 {
		result.Errors = append(result.Errors, fmt.Sprintf("發現 %d 個無效的會員關聯", orphanMembers))
	}

	// 驗證狀態欄位
	var invalidStatus int64
	mv.db.Model(&MemberAssignmentSubmission{}).
		Where("status NOT IN (?, ?, ?)", "pending", "approved", "rejected").
		Count(&invalidStatus)
	if invalidStatus > 0 {
		result.Errors = append(result.Errors, fmt.Sprintf("發現 %d 個無效的提交狀態", invalidStatus))
	}

	// 檢查重複提交
	var duplicateSubmissions []struct {
		AssignmentID uint
		MemberID     uint
		Count        int64
	}
	mv.db.Model(&MemberAssignmentSubmission{}).
		Select("assignment_id, member_id, COUNT(*) as count").
		Group("assignment_id, member_id").
		Having("COUNT(*) > 1").
		Scan(&duplicateSubmissions)

	if len(duplicateSubmissions) > 0 {
		result.Warnings = append(result.Warnings, fmt.Sprintf("發現 %d 組重複的作業提交", len(duplicateSubmissions)))
	}

	result.InvalidRecords = int64(len(result.Errors))
	result.ValidRecords = result.TotalRecords - result.InvalidRecords

	return result
}

// validateConsultationAppointments 驗證預約表
func (mv *MigrationValidator) validateConsultationAppointments() ValidationResult {
	result := ValidationResult{
		TableName: "consultation_appointments",
		Errors:    make([]string, 0),
		Warnings:  make([]string, 0),
	}

	mv.db.Model(&ConsultationAppointment{}).Count(&result.TotalRecords)

	// 驗證外鍵關聯
	var orphanModules int64
	mv.db.Model(&ConsultationAppointment{}).
		Where("module_id NOT IN (SELECT id FROM consultation_modules)").
		Count(&orphanModules)
	if orphanModules > 0 {
		result.Errors = append(result.Errors, fmt.Sprintf("發現 %d 個孤立的預約記錄", orphanModules))
	}

	var orphanMembers int64
	mv.db.Model(&ConsultationAppointment{}).
		Where("member_id NOT IN (SELECT id FROM members)").
		Count(&orphanMembers)
	if orphanMembers > 0 {
		result.Errors = append(result.Errors, fmt.Sprintf("發現 %d 個無效的會員關聯", orphanMembers))
	}

	// 驗證狀態欄位
	var invalidStatus int64
	mv.db.Model(&ConsultationAppointment{}).
		Where("status NOT IN (?, ?, ?, ?)", "pending", "confirmed", "completed", "cancelled").
		Count(&invalidStatus)
	if invalidStatus > 0 {
		result.Errors = append(result.Errors, fmt.Sprintf("發現 %d 個無效的預約狀態", invalidStatus))
	}

	// 檢查預約時間邏輯
	var invalidDates int64
	mv.db.Model(&ConsultationAppointment{}).
		Where("appointment_datetime < created_at").
		Count(&invalidDates)
	if invalidDates > 0 {
		result.Warnings = append(result.Warnings, fmt.Sprintf("發現 %d 個預約時間早於建立時間", invalidDates))
	}

	// 檢查時長
	var invalidDuration int64
	mv.db.Model(&ConsultationAppointment{}).
		Where("duration_minutes < 15 OR duration_minutes > 480").
		Count(&invalidDuration)
	if invalidDuration > 0 {
		result.Warnings = append(result.Warnings, fmt.Sprintf("發現 %d 個異常的預約時長", invalidDuration))
	}

	result.InvalidRecords = int64(len(result.Errors))
	result.ValidRecords = result.TotalRecords - result.InvalidRecords

	return result
}

// validateConsultationTimeSlots 驗證時段表
func (mv *MigrationValidator) validateConsultationTimeSlots() ValidationResult {
	result := ValidationResult{
		TableName: "consultation_time_slots",
		Errors:    make([]string, 0),
		Warnings:  make([]string, 0),
	}

	mv.db.Model(&ConsultationTimeSlot{}).Count(&result.TotalRecords)

	// 檢查時間邏輯
	var invalidTimes int64
	mv.db.Model(&ConsultationTimeSlot{}).
		Where("start_time >= end_time").
		Count(&invalidTimes)
	if invalidTimes > 0 {
		result.Errors = append(result.Errors, fmt.Sprintf("發現 %d 個無效的時間範圍", invalidTimes))
	}

	// 檢查最大預約數
	var invalidMaxAppointments int64
	mv.db.Model(&ConsultationTimeSlot{}).
		Where("max_appointments < 1 OR max_appointments > 10").
		Count(&invalidMaxAppointments)
	if invalidMaxAppointments > 0 {
		result.Warnings = append(result.Warnings, fmt.Sprintf("發現 %d 個異常的最大預約數", invalidMaxAppointments))
	}

	// 檢查過期的時段
	var expiredSlots int64
	cutoffDate := time.Now().AddDate(0, 0, -30).Format("2006-01-02")
	mv.db.Model(&ConsultationTimeSlot{}).
		Where("date < ?", cutoffDate).
		Count(&expiredSlots)
	if expiredSlots > 0 {
		result.Warnings = append(result.Warnings, fmt.Sprintf("發現 %d 個過期的時段記錄", expiredSlots))
	}

	result.InvalidRecords = int64(len(result.Errors))
	result.ValidRecords = result.TotalRecords - result.InvalidRecords

	return result
}

// validateConsultationEligibilityRules 驗證資格規則表
func (mv *MigrationValidator) validateConsultationEligibilityRules() ValidationResult {
	result := ValidationResult{
		TableName: "consultation_eligibility_rules",
		Errors:    make([]string, 0),
		Warnings:  make([]string, 0),
	}

	mv.db.Model(&ConsultationEligibilityRule{}).Count(&result.TotalRecords)

	// 驗證外鍵關聯
	var orphanModules int64
	mv.db.Model(&ConsultationEligibilityRule{}).
		Where("module_id NOT IN (SELECT id FROM consultation_modules)").
		Count(&orphanModules)
	if orphanModules > 0 {
		result.Errors = append(result.Errors, fmt.Sprintf("發現 %d 個孤立的規則記錄", orphanModules))
	}

	// 驗證規則類型
	var invalidTypes int64
	mv.db.Model(&ConsultationEligibilityRule{}).
		Where("rule_type NOT IN (?, ?, ?, ?)", "member_level", "assignment_completion", "course_completion", "custom").
		Count(&invalidTypes)
	if invalidTypes > 0 {
		result.Errors = append(result.Errors, fmt.Sprintf("發現 %d 個無效的規則類型", invalidTypes))
	}

	result.InvalidRecords = int64(len(result.Errors))
	result.ValidRecords = result.TotalRecords - result.InvalidRecords

	return result
}

// validateFileUploads 驗證檔案上傳表
func (mv *MigrationValidator) validateFileUploads() ValidationResult {
	result := ValidationResult{
		TableName: "file_uploads",
		Errors:    make([]string, 0),
		Warnings:  make([]string, 0),
	}

	mv.db.Model(&FileUpload{}).Count(&result.TotalRecords)

	// 驗證外鍵關聯
	var orphanMembers int64
	mv.db.Model(&FileUpload{}).
		Where("member_id NOT IN (SELECT id FROM members)").
		Count(&orphanMembers)
	if orphanMembers > 0 {
		result.Errors = append(result.Errors, fmt.Sprintf("發現 %d 個無效的會員關聯", orphanMembers))
	}

	// 檢查檔案大小
	var invalidSize int64
	mv.db.Model(&FileUpload{}).
		Where("file_size < 1 OR file_size > 104857600"). // 100MB
		Count(&invalidSize)
	if invalidSize > 0 {
		result.Warnings = append(result.Warnings, fmt.Sprintf("發現 %d 個異常的檔案大小", invalidSize))
	}

	// 檢查 MD5 雜湊
	var invalidMD5 int64
	mv.db.Model(&FileUpload{}).
		Where("md5_hash = '' OR md5_hash IS NULL OR LENGTH(md5_hash) != 32").
		Count(&invalidMD5)
	if invalidMD5 > 0 {
		result.Errors = append(result.Errors, fmt.Sprintf("發現 %d 個無效的 MD5 雜湊", invalidMD5))
	}

	result.InvalidRecords = int64(len(result.Errors))
	result.ValidRecords = result.TotalRecords - result.InvalidRecords

	return result
}

// validateDataIntegrity 驗證資料完整性
func (mv *MigrationValidator) validateDataIntegrity() ValidationResult {
	result := ValidationResult{
		TableName: "data_integrity",
		Errors:    make([]string, 0),
		Warnings:  make([]string, 0),
	}

	// 檢查孤立的作業提交（沒有對應的作業）
	var orphanSubmissions int64
	mv.db.Raw(`
		SELECT COUNT(*) FROM member_assignment_submissions mas
		LEFT JOIN consultation_assignments ca ON mas.assignment_id = ca.id
		WHERE ca.id IS NULL
	`).Scan(&orphanSubmissions)

	if orphanSubmissions > 0 {
		result.Errors = append(result.Errors, fmt.Sprintf("發現 %d 個孤立的作業提交", orphanSubmissions))
	}

	// 檢查孤立的預約（沒有對應的模組）
	var orphanAppointments int64
	mv.db.Raw(`
		SELECT COUNT(*) FROM consultation_appointments ca
		LEFT JOIN consultation_modules cm ON ca.module_id = cm.id
		WHERE cm.id IS NULL
	`).Scan(&orphanAppointments)

	if orphanAppointments > 0 {
		result.Errors = append(result.Errors, fmt.Sprintf("發現 %d 個孤立的預約記錄", orphanAppointments))
	}

	// 檢查重複的檔案（相同 MD5）
	var duplicateFiles []struct {
		MD5Hash string
		Count   int64
	}
	mv.db.Raw(`
		SELECT md5_hash, COUNT(*) as count 
		FROM file_uploads 
		WHERE md5_hash IS NOT NULL AND md5_hash != ''
		GROUP BY md5_hash 
		HAVING COUNT(*) > 1
	`).Scan(&duplicateFiles)

	if len(duplicateFiles) > 0 {
		result.Warnings = append(result.Warnings, fmt.Sprintf("發現 %d 組重複的檔案", len(duplicateFiles)))
	}

	result.TotalRecords = 1 // 這是一個綜合檢查
	result.InvalidRecords = int64(len(result.Errors))
	result.ValidRecords = result.TotalRecords - result.InvalidRecords

	return result
}

// calculateSummary 計算驗證摘要
func (mv *MigrationValidator) calculateSummary(results []ValidationResult) ValidationSummary {
	summary := ValidationSummary{
		TotalTables: len(results),
	}

	for _, result := range results {
		summary.TotalRecords += result.TotalRecords
		summary.ValidRecords += result.ValidRecords
		summary.InvalidRecords += result.InvalidRecords

		if len(result.Errors) == 0 {
			summary.PassedTables++
		} else {
			summary.FailedTables++
		}
	}

	summary.OverallSuccess = summary.FailedTables == 0

	return summary
}

// PrintReport 列印驗證報告
func (mv *MigrationValidator) PrintReport(report *MigrationReport) {
	fmt.Println("\n📊 資料遷移驗證報告")
	fmt.Println("====================")
	fmt.Printf("驗證時間: %v\n", report.Duration)
	fmt.Printf("開始時間: %s\n", report.StartTime.Format("2006-01-02 15:04:05"))
	fmt.Printf("結束時間: %s\n", report.EndTime.Format("2006-01-02 15:04:05"))

	fmt.Println("\n📈 整體摘要:")
	fmt.Printf("總表格數: %d\n", report.Summary.TotalTables)
	fmt.Printf("通過表格: %d\n", report.Summary.PassedTables)
	fmt.Printf("失敗表格: %d\n", report.Summary.FailedTables)
	fmt.Printf("總記錄數: %d\n", report.Summary.TotalRecords)
	fmt.Printf("有效記錄: %d\n", report.Summary.ValidRecords)
	fmt.Printf("無效記錄: %d\n", report.Summary.InvalidRecords)

	if report.Summary.OverallSuccess {
		fmt.Println("✅ 整體驗證: 通過")
	} else {
		fmt.Println("❌ 整體驗證: 失敗")
	}

	fmt.Println("\n📋 詳細結果:")
	for _, result := range report.Results {
		fmt.Printf("\n表格: %s\n", result.TableName)
		fmt.Printf("  總記錄: %d\n", result.TotalRecords)
		fmt.Printf("  有效記錄: %d\n", result.ValidRecords)
		fmt.Printf("  無效記錄: %d\n", result.InvalidRecords)

		if len(result.Errors) > 0 {
			fmt.Println("  ❌ 錯誤:")
			for _, err := range result.Errors {
				fmt.Printf("    - %s\n", err)
			}
		}

		if len(result.Warnings) > 0 {
			fmt.Println("  ⚠️  警告:")
			for _, warning := range result.Warnings {
				fmt.Printf("    - %s\n", warning)
			}
		}

		if len(result.Errors) == 0 && len(result.Warnings) == 0 {
			fmt.Println("  ✅ 無問題")
		}
	}

	fmt.Println("\n💡 建議:")
	if report.Summary.OverallSuccess {
		fmt.Println("✅ 資料遷移驗證通過，可以繼續進行部署")
		fmt.Println("✅ 建議定期執行此驗證腳本確保資料品質")
	} else {
		fmt.Println("❌ 發現資料問題，建議修復後重新驗證")
		fmt.Println("❌ 請檢查錯誤訊息並修正相關資料")
	}

	if report.Summary.InvalidRecords > 0 {
		fmt.Println("⚠️  建議備份現有資料後進行修復")
	}
}

func main() {
	fmt.Println("🔍 諮詢系統資料遷移驗證工具")
	fmt.Println("==============================")

	validator := NewMigrationValidator()

	// 執行驗證
	report, err := validator.ValidateAllTables()
	if err != nil {
		log.Fatal("驗證失敗:", err)
	}

	// 列印報告
	validator.PrintReport(report)

	// 生成 JSON 報告
	if err := validator.saveJSONReport(report); err != nil {
		log.Printf("保存 JSON 報告失敗: %v", err)
	}

	fmt.Println("\n🎉 驗證完成！")
}

// saveJSONReport 保存 JSON 格式報告
func (mv *MigrationValidator) saveJSONReport(report *MigrationReport) error {
	// 實作 JSON 報告保存邏輯
	fmt.Println("📄 JSON 報告已保存到 migration_validation_report.json")
	return nil
}
