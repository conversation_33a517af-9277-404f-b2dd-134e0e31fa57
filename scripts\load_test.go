//go:build ignore
// +build ignore

package main

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"math/rand"
	"net/http"
	"sync"
	"time"
)

// LoadTester 負載測試器
type LoadTester struct {
	baseURL     string
	concurrency int
	duration    time.Duration
	client      *http.Client
}

// TestResult 測試結果
type TestResult struct {
	TotalRequests   int           `json:"total_requests"`
	SuccessRequests int           `json:"success_requests"`
	FailedRequests  int           `json:"failed_requests"`
	AverageResponse time.Duration `json:"average_response"`
	MinResponse     time.Duration `json:"min_response"`
	MaxResponse     time.Duration `json:"max_response"`
	RequestsPerSec  float64       `json:"requests_per_second"`
	ErrorRate       float64       `json:"error_rate"`
	ResponseTimes   []time.Duration
	Errors          []string
}

// TestScenario 測試場景
type TestScenario struct {
	Name    string
	Method  string
	Path    string
	Body    interface{}
	Headers map[string]string
	Weight  int // 權重，決定此場景的執行頻率
}

// NewLoadTester 建立負載測試器
func NewLoadTester(baseURL string, concurrency int, duration time.Duration) *LoadTester {
	return &LoadTester{
		baseURL:     baseURL,
		concurrency: concurrency,
		duration:    duration,
		client: &http.Client{
			Timeout: 30 * time.Second,
		},
	}
}

// RunTest 執行負載測試
func (lt *LoadTester) RunTest() (*TestResult, error) {
	fmt.Printf("🚀 開始負載測試\n")
	fmt.Printf("目標: %s\n", lt.baseURL)
	fmt.Printf("並發數: %d\n", lt.concurrency)
	fmt.Printf("持續時間: %v\n", lt.duration)
	fmt.Println("==================")

	// 定義測試場景
	scenarios := []TestScenario{
		{
			Name:   "取得模組列表",
			Method: "GET",
			Path:   "/api/v2/modules",
			Weight: 30,
		},
		{
			Name:   "取得可用時段",
			Method: "GET",
			Path:   "/api/v2/available-slots",
			Weight: 20,
		},
		{
			Name:   "取得我的預約",
			Method: "GET",
			Path:   "/api/v2/appointments",
			Weight: 15,
		},
		{
			Name:   "檢查資格",
			Method: "GET",
			Path:   "/api/v2/modules/1/eligibility",
			Weight: 10,
		},
		{
			Name:   "提交作業",
			Method: "POST",
			Path:   "/api/v2/assignments/submit",
			Body: map[string]interface{}{
				"assignment_id": 1,
				"content":       "測試作業內容",
				"file_paths":    []string{},
			},
			Weight: 15,
		},
		{
			Name:   "建立預約",
			Method: "POST",
			Path:   "/api/v2/appointments",
			Body: map[string]interface{}{
				"module_id":            1,
				"title":                "測試預約",
				"description":          "負載測試預約",
				"appointment_datetime": time.Now().Add(24 * time.Hour).Format("2006-01-02 15:04:05"),
				"duration_minutes":     60,
			},
			Weight: 10,
		},
	}

	// 建立權重表
	weightedScenarios := lt.buildWeightedScenarios(scenarios)

	result := &TestResult{
		ResponseTimes: make([]time.Duration, 0),
		Errors:        make([]string, 0),
		MinResponse:   time.Hour, // 初始化為很大的值
	}

	var wg sync.WaitGroup
	var mu sync.Mutex

	startTime := time.Now()
	endTime := startTime.Add(lt.duration)

	// 啟動並發 goroutines
	for i := 0; i < lt.concurrency; i++ {
		wg.Add(1)
		go func(workerID int) {
			defer wg.Done()

			for time.Now().Before(endTime) {
				// 隨機選擇測試場景
				scenario := weightedScenarios[rand.Intn(len(weightedScenarios))]

				// 執行請求
				responseTime, err := lt.executeRequest(scenario)

				// 更新結果
				mu.Lock()
				result.TotalRequests++
				if err != nil {
					result.FailedRequests++
					result.Errors = append(result.Errors, fmt.Sprintf("[%s] %v", scenario.Name, err))
				} else {
					result.SuccessRequests++
					result.ResponseTimes = append(result.ResponseTimes, responseTime)

					if responseTime < result.MinResponse {
						result.MinResponse = responseTime
					}
					if responseTime > result.MaxResponse {
						result.MaxResponse = responseTime
					}
				}
				mu.Unlock()

				// 隨機延遲，模擬真實使用者行為
				time.Sleep(time.Duration(rand.Intn(100)) * time.Millisecond)
			}
		}(i)
	}

	// 等待所有 goroutines 完成
	wg.Wait()

	// 計算統計資料
	lt.calculateStatistics(result, time.Since(startTime))

	return result, nil
}

// buildWeightedScenarios 建立權重場景列表
func (lt *LoadTester) buildWeightedScenarios(scenarios []TestScenario) []TestScenario {
	var weighted []TestScenario

	for _, scenario := range scenarios {
		for i := 0; i < scenario.Weight; i++ {
			weighted = append(weighted, scenario)
		}
	}

	return weighted
}

// executeRequest 執行單個請求
func (lt *LoadTester) executeRequest(scenario TestScenario) (time.Duration, error) {
	url := lt.baseURL + scenario.Path

	var body io.Reader
	if scenario.Body != nil {
		jsonBody, err := json.Marshal(scenario.Body)
		if err != nil {
			return 0, fmt.Errorf("序列化請求體失敗: %w", err)
		}
		body = bytes.NewBuffer(jsonBody)
	}

	req, err := http.NewRequest(scenario.Method, url, body)
	if err != nil {
		return 0, fmt.Errorf("建立請求失敗: %w", err)
	}

	// 設置標頭
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("User-Agent", "LoadTester/1.0")

	// 模擬會員登入（簡化版本）
	req.Header.Set("Authorization", "Bearer test-token")

	for key, value := range scenario.Headers {
		req.Header.Set(key, value)
	}

	// 執行請求並測量時間
	start := time.Now()
	resp, err := lt.client.Do(req)
	responseTime := time.Since(start)

	if err != nil {
		return responseTime, fmt.Errorf("請求失敗: %w", err)
	}
	defer resp.Body.Close()

	// 讀取回應內容（避免連接洩漏）
	_, err = io.ReadAll(resp.Body)
	if err != nil {
		return responseTime, fmt.Errorf("讀取回應失敗: %w", err)
	}

	// 檢查 HTTP 狀態碼
	if resp.StatusCode >= 400 {
		return responseTime, fmt.Errorf("HTTP 錯誤: %d", resp.StatusCode)
	}

	return responseTime, nil
}

// calculateStatistics 計算統計資料
func (lt *LoadTester) calculateStatistics(result *TestResult, totalDuration time.Duration) {
	if len(result.ResponseTimes) > 0 {
		var total time.Duration
		for _, rt := range result.ResponseTimes {
			total += rt
		}
		result.AverageResponse = total / time.Duration(len(result.ResponseTimes))
	}

	result.RequestsPerSec = float64(result.TotalRequests) / totalDuration.Seconds()

	if result.TotalRequests > 0 {
		result.ErrorRate = float64(result.FailedRequests) / float64(result.TotalRequests) * 100
	}
}

// PrintResults 列印測試結果
func (lt *LoadTester) PrintResults(result *TestResult) {
	fmt.Println("\n📊 負載測試結果")
	fmt.Println("==================")
	fmt.Printf("總請求數: %d\n", result.TotalRequests)
	fmt.Printf("成功請求: %d\n", result.SuccessRequests)
	fmt.Printf("失敗請求: %d\n", result.FailedRequests)
	fmt.Printf("錯誤率: %.2f%%\n", result.ErrorRate)
	fmt.Printf("每秒請求數: %.2f\n", result.RequestsPerSec)
	fmt.Printf("平均回應時間: %v\n", result.AverageResponse)
	fmt.Printf("最小回應時間: %v\n", result.MinResponse)
	fmt.Printf("最大回應時間: %v\n", result.MaxResponse)

	// 計算百分位數
	if len(result.ResponseTimes) > 0 {
		fmt.Println("\n回應時間百分位數:")
		percentiles := []float64{50, 90, 95, 99}
		for _, p := range percentiles {
			value := lt.calculatePercentile(result.ResponseTimes, p)
			fmt.Printf("P%.0f: %v\n", p, value)
		}
	}

	// 顯示錯誤摘要
	if len(result.Errors) > 0 {
		fmt.Println("\n❌ 錯誤摘要:")
		errorCounts := make(map[string]int)
		for _, err := range result.Errors {
			errorCounts[err]++
		}

		for err, count := range errorCounts {
			fmt.Printf("  %s: %d 次\n", err, count)
		}
	}

	// 效能評估
	fmt.Println("\n🎯 效能評估:")
	if result.ErrorRate < 1 {
		fmt.Println("✅ 錯誤率良好 (< 1%)")
	} else if result.ErrorRate < 5 {
		fmt.Println("⚠️  錯誤率偏高 (1-5%)")
	} else {
		fmt.Println("❌ 錯誤率過高 (> 5%)")
	}

	if result.AverageResponse < 100*time.Millisecond {
		fmt.Println("✅ 回應時間優秀 (< 100ms)")
	} else if result.AverageResponse < 500*time.Millisecond {
		fmt.Println("⚠️  回應時間良好 (100-500ms)")
	} else if result.AverageResponse < 1*time.Second {
		fmt.Println("⚠️  回應時間偏慢 (500ms-1s)")
	} else {
		fmt.Println("❌ 回應時間過慢 (> 1s)")
	}

	if result.RequestsPerSec > 100 {
		fmt.Println("✅ 吞吐量優秀 (> 100 RPS)")
	} else if result.RequestsPerSec > 50 {
		fmt.Println("⚠️  吞吐量良好 (50-100 RPS)")
	} else {
		fmt.Println("❌ 吞吐量偏低 (< 50 RPS)")
	}
}

// calculatePercentile 計算百分位數
func (lt *LoadTester) calculatePercentile(times []time.Duration, percentile float64) time.Duration {
	if len(times) == 0 {
		return 0
	}

	// 簡單排序（實際應用中可能需要更高效的算法）
	sorted := make([]time.Duration, len(times))
	copy(sorted, times)

	// 冒泡排序（簡化實現）
	for i := 0; i < len(sorted); i++ {
		for j := 0; j < len(sorted)-1-i; j++ {
			if sorted[j] > sorted[j+1] {
				sorted[j], sorted[j+1] = sorted[j+1], sorted[j]
			}
		}
	}

	index := int(float64(len(sorted)) * percentile / 100)
	if index >= len(sorted) {
		index = len(sorted) - 1
	}

	return sorted[index]
}

// RunStressTest 執行壓力測試
func (lt *LoadTester) RunStressTest() {
	fmt.Println("🔥 執行壓力測試")
	fmt.Println("逐步增加負載，找出系統極限...")

	concurrencyLevels := []int{1, 5, 10, 20, 50, 100}

	for _, concurrency := range concurrencyLevels {
		fmt.Printf("\n測試並發數: %d\n", concurrency)
		fmt.Println("-------------------")

		lt.concurrency = concurrency
		lt.duration = 30 * time.Second // 每個級別測試30秒

		result, err := lt.RunTest()
		if err != nil {
			fmt.Printf("測試失敗: %v\n", err)
			continue
		}

		fmt.Printf("RPS: %.2f, 平均回應: %v, 錯誤率: %.2f%%\n",
			result.RequestsPerSec, result.AverageResponse, result.ErrorRate)

		// 如果錯誤率超過10%，停止測試
		if result.ErrorRate > 10 {
			fmt.Println("❌ 錯誤率過高，停止壓力測試")
			break
		}

		// 等待系統恢復
		time.Sleep(5 * time.Second)
	}
}

func main() {
	fmt.Println("🚀 諮詢系統負載測試工具")
	fmt.Println("========================")

	// 配置測試參數
	baseURL := "http://localhost:8080" // 修改為實際的服務地址
	concurrency := 10                  // 並發數
	duration := 60 * time.Second       // 測試持續時間

	tester := NewLoadTester(baseURL, concurrency, duration)

	// 執行負載測試
	result, err := tester.RunTest()
	if err != nil {
		log.Fatal("負載測試失敗:", err)
	}

	// 列印結果
	tester.PrintResults(result)

	// 詢問是否執行壓力測試
	fmt.Print("\n是否執行壓力測試？(y/N): ")
	var input string
	fmt.Scanln(&input)

	if input == "y" || input == "Y" {
		tester.RunStressTest()
	}

	fmt.Println("\n🎉 測試完成！")
	fmt.Println("\n建議:")
	fmt.Println("1. 根據測試結果調整系統配置")
	fmt.Println("2. 優化回應時間較慢的 API")
	fmt.Println("3. 增加快取機制減少資料庫負載")
	fmt.Println("4. 考慮實施限流機制")
	fmt.Println("5. 監控生產環境的實際效能")
}
