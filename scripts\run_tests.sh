#!/bin/bash

# 諮詢系統 V2 測試運行腳本

set -e

echo "=== 諮詢系統 V2 測試套件 ==="
echo "開始時間: $(date)"
echo ""

# 檢查環境
echo "檢查測試環境..."

# 檢查 Go 版本
if ! command -v go &> /dev/null; then
    echo "錯誤: Go 未安裝"
    exit 1
fi

echo "Go 版本: $(go version)"

# 檢查資料庫連接
echo "檢查測試資料庫連接..."
if ! mysql -h localhost -u root -pforwork0926 -e "SELECT 1" &> /dev/null; then
    echo "錯誤: 無法連接到測試資料庫"
    echo "請確認 MySQL 服務正在運行且認證資訊正確"
    exit 1
fi

# 建立測試資料庫（如果不存在）
echo "準備測試資料庫..."
mysql -h localhost -u root -pforwork0926 -e "CREATE DATABASE IF NOT EXISTS grace_test CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"

echo "測試環境檢查完成"
echo ""

# 設置測試環境變數
export GO_ENV=test
export DB_NAME=grace_test

# 清理之前的測試結果
echo "清理測試環境..."
rm -rf ./test_uploads
rm -f ./coverage.out
rm -f ./test_results.xml

# 下載測試依賴
echo "下載測試依賴..."
go mod download
go mod tidy

echo ""
echo "=== 開始運行測試 ==="
echo ""

# 檢查測試類型參數
TEST_TYPE=${1:-all}

case $TEST_TYPE in
    "unit")
        echo "1. 運行單元測試..."
        echo "----------------------------------------"
        go test -v -race -coverprofile=coverage_unit.out ./test/consultation_v2_test.go || {
            echo "單元測試失敗"
            exit 1
        }
        ;;
    "integration")
        echo "2. 運行整合測試..."
        echo "----------------------------------------"
        go test -v -race -coverprofile=coverage_integration.out ./test/integration_test.go || {
            echo "整合測試失敗"
            exit 1
        }
        ;;
    "e2e")
        echo "3. 運行端到端測試..."
        echo "----------------------------------------"
        go test -v -race -coverprofile=coverage_e2e.out ./test/e2e_test.go || {
            echo "端到端測試失敗"
            exit 1
        }
        ;;
    "performance")
        echo "4. 運行效能測試..."
        echo "----------------------------------------"
        go test -v -timeout=10m ./test/performance_test.go || {
            echo "效能測試失敗"
            exit 1
        }
        ;;
    "benchmark")
        echo "5. 運行基準測試..."
        echo "----------------------------------------"
        go test -bench=. -benchmem ./test/performance_test.go > benchmark_results.txt || {
            echo "基準測試失敗"
            exit 1
        }
        ;;
    "all"|*)
        echo "1. 運行單元測試..."
        echo "----------------------------------------"
        go test -v -race -coverprofile=coverage_unit.out ./test/consultation_v2_test.go || {
            echo "單元測試失敗"
            exit 1
        }

        echo ""
        echo "2. 運行整合測試..."
        echo "----------------------------------------"
        go test -v -race -coverprofile=coverage_integration.out ./test/integration_test.go || {
            echo "整合測試失敗"
            exit 1
        }

        echo ""
        echo "3. 運行端到端測試..."
        echo "----------------------------------------"
        go test -v -race -coverprofile=coverage_e2e.out ./test/e2e_test.go || {
            echo "端到端測試失敗"
            exit 1
        }

        echo ""
        echo "4. 運行效能測試..."
        echo "----------------------------------------"
        go test -v -timeout=10m ./test/performance_test.go || {
            echo "效能測試失敗"
            exit 1
        }

        echo ""
        echo "5. 運行基準測試..."
        echo "----------------------------------------"
        go test -bench=. -benchmem ./test/performance_test.go > benchmark_results.txt || {
            echo "基準測試失敗"
            exit 1
        }
        ;;
esac

# 生成測試報告（僅在執行所有測試時）
if [ "$TEST_TYPE" = "all" ] || [ "$TEST_TYPE" = "" ]; then
    echo ""
    echo "=== 生成測試報告 ==="
    echo ""

    # 合併覆蓋率文件
    echo "合併覆蓋率報告..."
    echo "mode: atomic" > coverage.out
    grep -h -v "^mode:" coverage_*.out >> coverage.out 2>/dev/null || true

    # 生成覆蓋率報告
    if [ -f coverage.out ]; then
        echo "生成覆蓋率報告..."
        go tool cover -html=coverage.out -o coverage.html

        # 顯示覆蓋率統計
        echo ""
        echo "覆蓋率統計:"
        go tool cover -func=coverage.out | tail -1

        echo ""
        echo "詳細覆蓋率報告已生成: coverage.html"
    fi

    # 清理臨時文件
    rm -f coverage_*.out
fi

echo ""
echo "=== 測試完成 ==="
echo "結束時間: $(date)"
echo ""

# 檢查是否有測試失敗
if [ $? -eq 0 ]; then
    echo "✅ 測試通過！"
    echo ""

    if [ "$TEST_TYPE" = "all" ] || [ "$TEST_TYPE" = "" ]; then
        echo "📊 生成的報告:"
        echo "- 覆蓋率報告: coverage.html"
        echo "- 基準測試結果: benchmark_results.txt"
        echo ""
        echo "下一步建議:"
        echo "1. 檢查覆蓋率報告 (coverage.html)"
        echo "2. 運行資料遷移測試"
        echo "3. 進行手動功能測試"
        echo "4. 準備部署到測試環境"
    fi

    echo ""
    echo "💡 使用方法:"
    echo "  ./run_tests.sh unit        - 只執行單元測試"
    echo "  ./run_tests.sh integration - 只執行整合測試"
    echo "  ./run_tests.sh e2e         - 只執行端到端測試"
    echo "  ./run_tests.sh performance - 只執行效能測試"
    echo "  ./run_tests.sh benchmark   - 只執行基準測試"
    echo "  ./run_tests.sh all         - 執行所有測試（預設）"
else
    echo "❌ 測試失敗，請檢查錯誤信息"
    exit 1
fi
