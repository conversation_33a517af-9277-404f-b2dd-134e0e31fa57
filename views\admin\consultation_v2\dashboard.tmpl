{{define "admin/consultation_v2/dashboard"}}
<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>諮詢系統總覽 - G.R.A.C.E 優雅學院後台</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .dashboard-card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            margin-bottom: 20px;
        }
        .dashboard-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 15px rgba(0, 0, 0, 0.2);
        }
        .dashboard-card .card-body {
            padding: 30px;
        }
        .dashboard-card h3 {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 10px;
        }
        .dashboard-card .card-title {
            font-size: 1.1rem;
            opacity: 0.9;
        }
        .quick-action-card {
            border: 1px solid #e0e0e0;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            transition: all 0.3s ease;
            background: white;
            margin-bottom: 20px;
        }
        .quick-action-card:hover {
            border-color: #007bff;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            transform: translateY(-2px);
        }
        .quick-action-card i {
            font-size: 2.5rem;
            margin-bottom: 15px;
            color: #007bff;
        }
        .recent-activity {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .activity-item {
            padding: 15px 0;
            border-bottom: 1px solid #f0f0f0;
        }
        .activity-item:last-child {
            border-bottom: none;
        }
        .activity-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
        }
        .activity-pending { background: #fff3cd; color: #856404; }
        .activity-confirmed { background: #d1ecf1; color: #0c5460; }
        .activity-completed { background: #d4edda; color: #155724; }
        .chart-container {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .alert-item {
            border-left: 4px solid #dc3545;
            background: #f8d7da;
            padding: 15px;
            margin-bottom: 10px;
            border-radius: 0 5px 5px 0;
        }
    </style>
</head>
<body>
    <div class="container-fluid mt-4">
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2><i class="fas fa-tachometer-alt"></i> 諮詢系統總覽</h2>
                    <div>
                        <button class="btn btn-outline-primary" onclick="refreshDashboard()">
                            <i class="fas fa-sync-alt"></i> 重新整理
                        </button>
                        <a href="/admin" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left"></i> 返回後台首頁
                        </a>
                    </div>
                </div>

                <!-- 統計卡片 -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="dashboard-card">
                            <div class="card-body">
                                <h3 id="totalModules">0</h3>
                                <p class="card-title"><i class="fas fa-puzzle-piece"></i> 諮詢模組</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="dashboard-card">
                            <div class="card-body">
                                <h3 id="pendingAppointments">0</h3>
                                <p class="card-title"><i class="fas fa-clock"></i> 待確認預約</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="dashboard-card">
                            <div class="card-body">
                                <h3 id="todayAppointments">0</h3>
                                <p class="card-title"><i class="fas fa-calendar-day"></i> 今日預約</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="dashboard-card">
                            <div class="card-body">
                                <h3 id="pendingSubmissions">0</h3>
                                <p class="card-title"><i class="fas fa-file-alt"></i> 待審核作業</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 快速操作 -->
                <div class="row mb-4">
                    <div class="col-12">
                        <h5><i class="fas fa-bolt"></i> 快速操作</h5>
                    </div>
                    <div class="col-md-2">
                        <a href="/admin/consultations/v2/modules" class="text-decoration-none">
                            <div class="quick-action-card">
                                <i class="fas fa-puzzle-piece"></i>
                                <h6>模組管理</h6>
                                <p class="text-muted small">管理諮詢模組</p>
                            </div>
                        </a>
                    </div>
                    <div class="col-md-2">
                        <a href="/admin/consultations/v2/appointments" class="text-decoration-none">
                            <div class="quick-action-card">
                                <i class="fas fa-calendar-check"></i>
                                <h6>預約管理</h6>
                                <p class="text-muted small">處理預約申請</p>
                            </div>
                        </a>
                    </div>
                    <div class="col-md-2">
                        <a href="/admin/consultations/v2/time-slots" class="text-decoration-none">
                            <div class="quick-action-card">
                                <i class="fas fa-clock"></i>
                                <h6>時段管理</h6>
                                <p class="text-muted small">設定可預約時段</p>
                            </div>
                        </a>
                    </div>
                    <div class="col-md-2">
                        <a href="/admin/consultations/v2/assignments" class="text-decoration-none">
                            <div class="quick-action-card">
                                <i class="fas fa-tasks"></i>
                                <h6>作業審核</h6>
                                <p class="text-muted small">審核會員作業</p>
                            </div>
                        </a>
                    </div>
                    <div class="col-md-2">
                        <a href="/admin/consultations/v2/statistics" class="text-decoration-none">
                            <div class="quick-action-card">
                                <i class="fas fa-chart-bar"></i>
                                <h6>統計報表</h6>
                                <p class="text-muted small">查看數據分析</p>
                            </div>
                        </a>
                    </div>
                    <div class="col-md-2">
                        <a href="/admin/consultations/v2/settings" class="text-decoration-none">
                            <div class="quick-action-card">
                                <i class="fas fa-cog"></i>
                                <h6>系統設定</h6>
                                <p class="text-muted small">配置系統參數</p>
                            </div>
                        </a>
                    </div>
                </div>

                <div class="row">
                    <!-- 最近活動 -->
                    <div class="col-md-6">
                        <div class="recent-activity">
                            <h5><i class="fas fa-history"></i> 最近活動</h5>
                            <div id="recentActivities">
                                <!-- 活動項目將由 JavaScript 動態載入 -->
                            </div>
                            <div class="text-center mt-3">
                                <a href="/admin/consultations/v2/audit-logs" class="btn btn-outline-primary btn-sm">
                                    查看完整日誌
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- 系統警告 -->
                    <div class="col-md-6">
                        <div class="recent-activity">
                            <h5><i class="fas fa-exclamation-triangle"></i> 系統警告</h5>
                            <div id="systemAlerts">
                                <!-- 警告項目將由 JavaScript 動態載入 -->
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 圖表區域 -->
                <div class="row mt-4">
                    <div class="col-md-8">
                        <div class="chart-container">
                            <h5><i class="fas fa-chart-line"></i> 預約趨勢</h5>
                            <canvas id="appointmentChart" width="400" height="200"></canvas>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="chart-container">
                            <h5><i class="fas fa-pie-chart"></i> 模組分布</h5>
                            <canvas id="moduleChart" width="200" height="200"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        let appointmentChart, moduleChart;

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadDashboardData();
            initializeCharts();
        });

        // 載入總覽資料
        function loadDashboardData() {
            fetch('/api/admin/v2/dashboard')
            .then(response => response.json())
            .then(data => {
                if (data.msg === '取得總覽資料成功') {
                    updateStatistics(data.data.statistics);
                    updateRecentActivities(data.data.recent_activities);
                    updateSystemAlerts(data.data.system_alerts);
                    updateCharts(data.data.charts);
                }
            })
            .catch(error => {
                console.error('載入總覽資料失敗:', error);
            });
        }

        // 更新統計數據
        function updateStatistics(stats) {
            document.getElementById('totalModules').textContent = stats.total_modules || 0;
            document.getElementById('pendingAppointments').textContent = stats.pending_appointments || 0;
            document.getElementById('todayAppointments').textContent = stats.today_appointments || 0;
            document.getElementById('pendingSubmissions').textContent = stats.pending_submissions || 0;
        }

        // 更新最近活動
        function updateRecentActivities(activities) {
            const container = document.getElementById('recentActivities');
            
            if (!activities || activities.length === 0) {
                container.innerHTML = '<p class="text-muted text-center">暫無最近活動</p>';
                return;
            }

            container.innerHTML = activities.map(activity => `
                <div class="activity-item d-flex align-items-center">
                    <div class="activity-icon activity-${activity.type}">
                        <i class="fas fa-${getActivityIcon(activity.type)}"></i>
                    </div>
                    <div class="flex-grow-1">
                        <div class="fw-bold">${activity.title}</div>
                        <div class="text-muted small">${activity.description}</div>
                        <div class="text-muted small">${formatDateTime(activity.created_at)}</div>
                    </div>
                </div>
            `).join('');
        }

        // 更新系統警告
        function updateSystemAlerts(alerts) {
            const container = document.getElementById('systemAlerts');
            
            if (!alerts || alerts.length === 0) {
                container.innerHTML = '<p class="text-muted text-center">系統運行正常</p>';
                return;
            }

            container.innerHTML = alerts.map(alert => `
                <div class="alert-item">
                    <div class="fw-bold">${alert.title}</div>
                    <div class="small">${alert.message}</div>
                    <div class="text-muted small">${formatDateTime(alert.created_at)}</div>
                </div>
            `).join('');
        }

        // 初始化圖表
        function initializeCharts() {
            // 預約趨勢圖表
            const appointmentCtx = document.getElementById('appointmentChart').getContext('2d');
            appointmentChart = new Chart(appointmentCtx, {
                type: 'line',
                data: {
                    labels: [],
                    datasets: [{
                        label: '預約數量',
                        data: [],
                        borderColor: 'rgb(75, 192, 192)',
                        backgroundColor: 'rgba(75, 192, 192, 0.2)',
                        tension: 0.1
                    }]
                },
                options: {
                    responsive: true,
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });

            // 模組分布圖表
            const moduleCtx = document.getElementById('moduleChart').getContext('2d');
            moduleChart = new Chart(moduleCtx, {
                type: 'doughnut',
                data: {
                    labels: [],
                    datasets: [{
                        data: [],
                        backgroundColor: [
                            '#FF6384',
                            '#36A2EB',
                            '#FFCE56',
                            '#4BC0C0',
                            '#9966FF'
                        ]
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false
                }
            });
        }

        // 更新圖表
        function updateCharts(chartData) {
            if (chartData.appointment_trend) {
                appointmentChart.data.labels = chartData.appointment_trend.labels;
                appointmentChart.data.datasets[0].data = chartData.appointment_trend.data;
                appointmentChart.update();
            }

            if (chartData.module_distribution) {
                moduleChart.data.labels = chartData.module_distribution.labels;
                moduleChart.data.datasets[0].data = chartData.module_distribution.data;
                moduleChart.update();
            }
        }

        // 取得活動圖示
        function getActivityIcon(type) {
            const iconMap = {
                'appointment': 'calendar-check',
                'submission': 'file-alt',
                'module': 'puzzle-piece',
                'user': 'user',
                'system': 'cog'
            };
            return iconMap[type] || 'info-circle';
        }

        // 格式化日期時間
        function formatDateTime(dateTimeStr) {
            const date = new Date(dateTimeStr);
            return date.toLocaleString('zh-TW', {
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit'
            });
        }

        // 重新整理總覽
        function refreshDashboard() {
            loadDashboardData();
        }

        // 定期更新資料（每5分鐘）
        setInterval(loadDashboardData, 5 * 60 * 1000);
    </script>
</body>
</html>
{{end}}
